# 视频流推送功能使用指南

本指南详细介绍如何使用缺陷检测系统的视频流推送功能。

## 功能概述

视频流推送功能允许将摄像头获取的实时视频流通过RTSP协议推送到网络，支持多客户端同时观看。

### 主要特性

- **实时H.264编码**：使用FFmpeg进行高效视频编码
- **RTSP协议支持**：标准RTSP协议，兼容各种播放器
- **自适应质量控制**：根据网络状况自动调整码率和帧率
- **多客户端支持**：支持多个客户端同时连接观看
- **低延迟设计**：优化的编码和传输参数，延迟低于500ms
- **高内聚低耦合**：不影响现有缺陷检测功能的性能

## 系统要求

### 硬件要求
- CPU: 多核处理器（推荐4核以上）
- 内存: 4GB以上（推流功能额外需要1GB）
- 网络: 100Mbps以上带宽（用于多客户端推流）

### 软件要求
- FFmpeg 4.0+（必需）
- MediaMTX或其他RTSP服务器（推荐）
- VLC媒体播放器（用于测试）

## 安装配置

### 1. 安装FFmpeg

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install ffmpeg libavcodec-dev libavformat-dev libavutil-dev libswscale-dev
```

**CentOS/RHEL:**
```bash
sudo yum install epel-release
sudo yum install ffmpeg ffmpeg-devel
```

### 2. 安装MediaMTX RTSP服务器

```bash
# 下载最新版本
wget https://github.com/bluenviron/mediamtx/releases/latest/download/mediamtx_linux_amd64.tar.gz
tar -xzf mediamtx_linux_amd64.tar.gz
chmod +x mediamtx

# 启动服务器
./mediamtx
```

### 3. 编译系统

```bash
cd fault_detect
mkdir -p build
cd build
cmake ..
make -j$(nproc)
```

## 配置说明

### 推流配置

编辑 `config/system_config.json` 文件：

```json
{
  "streaming": {
    "enabled": true,
    "mode": "external_server",
    "server_type": "mediamtx",
    "push_url": "rtmp://localhost:1935/live",
    "view_url": "rtsp://localhost:8554/live",
    "video_encoding": {
      "codec": "h264",
      "width": 640,
      "height": 480,
      "fps": 15,
      "bitrate": 2000000,
      "preset": "fast",
      "profile": "baseline"
    },
    "connection": {
      "connect_timeout_ms": 5000,
      "reconnect_interval_ms": 5000,
      "max_retries": 3
    },
    "quality_control": {
      "adaptive_bitrate": true,
      "min_bitrate": 500000,
      "max_bitrate": 5000000,
      "frame_drop_threshold": 0.1,
      "buffer_size": 1048576,
      "max_queue_size": 30
    }
  }
}
```

### 配置参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `enabled` | 是否启用推流功能 | `false` |
| `push_url` | 推流地址（RTMP） | `rtmp://localhost:1935/live` |
| `view_url` | 观看地址（RTSP） | `rtsp://localhost:8554/live` |
| `codec` | 视频编码格式 | `h264` |
| `width/height` | 视频分辨率 | `640x480` |
| `fps` | 帧率 | `15` |
| `bitrate` | 码率（bps） | `2000000` |
| `preset` | 编码预设 | `fast` |
| `adaptive_bitrate` | 自适应码率 | `true` |

## 使用方法

### 1. 启动系统

```bash
cd build
./bin/FaultDetectRefactored
```

系统启动后会显示推流状态：

```
=== 系统状态 ===
摄像头 0: 采集中 (15.0 fps) [640x480]
推流状态: 运行中 (15.0 fps, 1950 kbps)
推流地址: rtsp://localhost:8554/live
```

### 2. 观看视频流

使用VLC媒体播放器：

1. 打开VLC
2. 选择"媒体" -> "打开网络串流"
3. 输入地址：`rtsp://localhost:8554/live`
4. 点击"播放"

使用ffplay命令行：

```bash
ffplay rtsp://localhost:8554/live
```

### 3. 运行测试

使用集成测试脚本：

```bash
chmod +x scripts/test_streaming.sh
./scripts/test_streaming.sh
```

或运行单元测试：

```bash
cd build
./bin/FaultDetectRefactored --test streaming
```

## 性能优化

### 1. 编码参数优化

**低延迟配置：**
```json
{
  "preset": "ultrafast",
  "profile": "baseline",
  "fps": 30,
  "bitrate": 1000000
}
```

**高质量配置：**
```json
{
  "preset": "slow",
  "profile": "high",
  "fps": 15,
  "bitrate": 4000000
}
```

### 2. 网络优化

- 确保网络带宽充足
- 使用有线网络连接
- 调整防火墙设置开放RTSP端口（8554）

### 3. 系统优化

- 增加系统内存
- 使用SSD存储
- 关闭不必要的系统服务

## 故障排除

### 常见问题

**1. 推流连接失败**
```
错误: StreamClient: 无法打开输出URL
解决: 检查MediaMTX服务器是否运行，端口是否被占用
```

**2. 视频编码失败**
```
错误: VideoEncoder: 找不到编码器: h264
解决: 确保FFmpeg正确安装并包含H.264编码器
```

**3. 帧率过低**
```
现象: 推流帧率明显低于配置值
解决: 检查CPU使用率，调整编码预设为"ultrafast"
```

**4. 播放器无法连接**
```
现象: VLC显示"无法打开MRL"
解决: 检查RTSP服务器状态，确认地址和端口正确
```

### 调试方法

**1. 查看系统日志**
```bash
# 系统会输出详细的推流状态信息
./bin/FaultDetectRefactored | grep -E "(StreamClient|VideoEncoder|RTSPStreamManager)"
```

**2. 检查网络连接**
```bash
# 检查RTSP端口
netstat -tuln | grep 8554

# 测试RTSP连接
ffprobe rtsp://localhost:8554/live
```

**3. 监控系统资源**
```bash
# 监控CPU和内存使用
top -p $(pgrep FaultDetectRefactored)
```

## API接口

### C++ API

```cpp
#include "rtsp_stream_manager.h"

// 创建推流管理器
RTSPStreamManager streamManager;

// 配置推流参数
RTSPStreamConfig config;
config.enabled = true;
config.codec = "h264";
config.width = 640;
config.height = 480;
config.fps = 15;
config.bitrate = 2000000;

// 初始化和启动
streamManager.initialize(config);
streamManager.start();

// 推送视频帧
cv::Mat frame = ...; // 获取视频帧
streamManager.pushFrame(frame, 0);

// 获取统计信息
const auto& stats = streamManager.getStats();
std::cout << "FPS: " << stats.currentFPS.load() << std::endl;
std::cout << "Bitrate: " << stats.avgBitrate.load() << std::endl;

// 停止推流
streamManager.stop();
```

## 技术架构

### 组件结构

```
RTSPStreamManager (推流管理器)
├── VideoEncoder (视频编码器)
│   ├── FFmpeg H.264编码
│   ├── 自适应码率控制
│   └── 硬件加速支持
├── StreamClient (推流客户端)
│   ├── RTMP推流
│   ├── 自动重连
│   └── 连接状态监控
└── QualityController (质量控制)
    ├── 帧率控制
    ├── 缓冲区管理
    └── 丢帧策略
```

### 数据流

```
摄像头 -> 缺陷检测 -> 视频编码 -> 推流客户端 -> RTSP服务器 -> 播放器
                 ↓
              质量控制
```

## 更新日志

### v1.0.0 (2025-07-31)
- 初始版本发布
- 支持H.264视频编码
- 支持RTSP协议推流
- 实现自适应质量控制
- 集成到缺陷检测系统

## 技术支持

如有问题或建议，请联系开发团队。

---

**注意：** 本功能需要FFmpeg库支持，请确保正确安装相关依赖。
