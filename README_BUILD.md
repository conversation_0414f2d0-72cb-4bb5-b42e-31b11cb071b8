# 拉吊索缺损识别系统 - 构建说明

## 📋 项目概述

本项目是一个基于OpenCV的拉吊索缺损识别系统，采用重构后的架构设计，具有高内聚低耦合的特点。

## 🚀 快速开始

### 方法一：使用构建脚本（推荐）

```bash
# 克隆或进入项目目录
cd fault_detect

# 运行构建脚本
./build.sh
```

### 方法二：手动构建

```bash
# 创建构建目录
mkdir -p build
cd build

# 配置项目
cmake ..

# 编译
make

# 运行
./bin/FaultDetectRefactored
```

## 📦 依赖要求

### 必需依赖
- **CMake** >= 3.10
- **C++17** 编译器
- **线程库** (pthread)

### 可选依赖
- **OpenCV** >= 4.0 (推荐，用于完整功能)
- **FFmpeg** (用于RTSP推流功能)

## 🎯 构建目标

### 默认构建（重构版本）
- **有OpenCV**: 构建完整功能版本，使用 `main_refactored.cpp`
- **无OpenCV**: 构建简化版本，基础功能测试

### 生成的可执行文件
- `build/bin/FaultDetectRefactored`

## 💻 使用方法

```bash
cd build

# 生产模式（默认）
./bin/FaultDetectRefactored

# 查看帮助
./bin/FaultDetectRefactored --help

# 运行测试
./bin/FaultDetectRefactored --test all      # 所有测试
./bin/FaultDetectRefactored --test basic    # 基础测试
./bin/FaultDetectRefactored --test camera   # 摄像头测试
./bin/FaultDetectRefactored --test system   # 系统测试
```

## 🏗️ 架构特点

### 重构优势
1. **单一职责**: 主程序专注缺损识别核心业务
2. **模块分离**: 测试、检测、管理功能完全独立
3. **高内聚低耦合**: 模块间依赖最小化
4. **易于扩展**: 新功能不影响现有模块

### 核心模块
- **主程序**: `main_refactored.cpp` - 生产环境核心逻辑
- **测试管理**: `test/test_manager.cpp` - 独立的测试框架
- **检测引擎**: `damage_detection_engine.cpp` - 缺损检测算法
- **摄像头管理**: `camera/camera_manager.cpp` - 多摄像头管理

## 🔧 配置文件

- `config/system_config.json` - 系统配置参数
- 构建后自动复制到 `build/config/`

## 📁 输出目录

- `build/output/` - 检测结果和图像输出
- `build/output/detection_results/` - 检测结果数据
- `build/output/detection_images/` - 检测结果图像

## ⚠️ 注意事项

1. **OpenCV版本**: 推荐使用OpenCV 4.0+
2. **摄像头权限**: 确保有摄像头访问权限
3. **存储空间**: 确保有足够空间存储检测结果
4. **网络环境**: RTSP推流需要网络支持

## 🐛 故障排除

### 编译错误
- 检查CMake版本和C++17支持
- 确认OpenCV安装路径
- 检查依赖库是否完整

### 运行错误
- 检查配置文件是否存在
- 确认摄像头设备可用
- 查看日志输出定位问题

## 📝 更新记录

详细的开发记录请查看 `readme.txt` 文件。

---

**版本**: 1.0.0  
**更新日期**: 2025-07-30  
**架构**: 重构版本（默认构建目标）
