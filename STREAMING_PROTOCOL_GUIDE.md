# 推流协议实现方案指南

## 协议对比分析

### RTMP vs RTSP 详细对比

| 特性 | RTMP | RTSP | 推荐度 |
|------|------|------|--------|
| **延迟性能** | 1-3秒 | 100-500ms | ⭐⭐⭐ RTSP |
| **兼容性** | 直播平台广泛支持 | 工业监控标准 | ⭐⭐⭐ RTSP |
| **实现复杂度** | 中等（需FLV格式） | 简单（标准化） | ⭐⭐⭐ RTSP |
| **网络穿透** | TCP，较好 | UDP/TCP可选 | ⭐⭐ 平分 |
| **客户端支持** | 需专门播放器 | VLC原生支持 | ⭐⭐⭐ RTSP |

### 基于故障检测场景的分析

**项目特点：**
- 拉吊索缺损识别系统
- 实时监控和故障检测
- 工业环境应用
- 对延迟敏感

**推荐协议：RTSP**
- ✅ 极低延迟（100-500ms）适合实时故障检测
- ✅ 工业监控行业标准协议
- ✅ VLC、专业监控软件原生支持
- ✅ 实现简单，稳定性好

## 实现方案

### 方案A：修复RTMP推流（已实现）

**问题诊断：**
原代码中`avformat_alloc_output_context2`未明确指定输出格式，导致FFmpeg无法识别RTMP流的正确格式。

**修复内容：**
```cpp
// 修复前
int ret = avformat_alloc_output_context2(&formatContext_, nullptr, nullptr, config_.pushUrl.c_str());

// 修复后
const char* format_name = nullptr;
if (config_.pushUrl.find("rtmp://") == 0) {
    format_name = "flv";  // RTMP使用FLV格式
} else if (config_.pushUrl.find("rtsp://") == 0) {
    format_name = "rtsp"; // RTSP格式
}
int ret = avformat_alloc_output_context2(&formatContext_, nullptr, format_name, config_.pushUrl.c_str());
```

**测试验证：**
```bash
# 重新编译
cd build && make

# 运行测试
./bin/FaultDetect --test streaming
```

### 方案B：纯RTSP推流（推荐）

**配置修改：**
已将`config/system_config.json`中的推流地址修改为RTSP：
```json
{
  "streaming": {
    "push_url": "rtsp://localhost:8554/live",
    "view_url": "rtsp://localhost:8554/live"
  }
}
```

**优势：**
1. **更低延迟**：直接RTSP推流，延迟100-500ms
2. **更简单**：避免RTMP到RTSP的转换
3. **更稳定**：减少协议转换环节
4. **更标准**：监控行业标准协议

## 依赖和配置

### FFmpeg配置验证

**检查协议支持：**
```bash
# 检查RTMP支持
ffmpeg -protocols | grep rtmp

# 检查RTSP支持  
ffmpeg -protocols | grep rtsp

# 检查格式支持
ffmpeg -formats | grep -E "(flv|rtsp)"
```

**当前系统状态：**
- ✅ FFmpeg 4.4.2已安装
- ✅ RTMP协议支持已确认
- ✅ FLV格式支持已确认
- ✅ RTSP协议支持已确认

### MediaMTX服务器配置

**当前配置状态：**
- ✅ RTSP端口：8554
- ✅ RTMP端口：1935  
- ✅ 支持多客户端连接
- ✅ 自动启动脚本已配置

**服务管理：**
```bash
# 启动MediaMTX
./scripts/setup_rtsp_server.sh start

# 检查状态
./scripts/setup_rtsp_server.sh status

# 停止服务
./scripts/setup_rtsp_server.sh stop
```

## 测试验证方法

### 1. 编译测试
```bash
cd build
make clean && make
```

### 2. 功能测试
```bash
# 运行推流测试
./bin/FaultDetect --test streaming

# 运行离线测试
./bin/FaultDetect --test streaming_offline
```

### 3. 手动验证
```bash
# 启动MediaMTX服务器
./scripts/setup_rtsp_server.sh start

# 使用FFmpeg推流测试
ffmpeg -f lavfi -i testsrc=duration=10:size=640x480:rate=15 \
       -c:v libx264 -preset fast -b:v 1000k \
       -f rtsp rtsp://localhost:8554/live

# 使用VLC观看
vlc rtsp://localhost:8554/live
```

### 4. 性能测试
```bash
# 运行性能测试脚本
./scripts/performance_test.sh

# 运行兼容性测试
./scripts/compatibility_test.sh
```

## 代码修改建议

### 当前修改状态
1. ✅ **修复RTMP格式问题**：在`stream_client.cpp`中添加格式检测
2. ✅ **配置RTSP推流**：修改`system_config.json`使用RTSP地址
3. ⏳ **待测试验证**：需要重新编译和测试

### 建议的进一步优化
1. **添加协议自动检测**：根据URL自动选择最优协议
2. **增强错误处理**：添加更详细的协议错误信息
3. **性能监控**：添加推流质量监控指标

## 解决方案总结

### 短期解决方案（立即可用）
1. **使用修复后的RTMP推流**：已修复格式问题
2. **切换到RTSP推流**：配置已更新，更适合项目需求

### 长期优化方案
1. **协议自适应**：根据网络条件自动选择协议
2. **多协议支持**：同时支持RTMP和RTSP
3. **质量自适应**：根据网络状况动态调整参数

### 推荐实施步骤
1. **立即实施**：重新编译并测试修复后的代码
2. **验证功能**：使用测试脚本验证推流功能
3. **性能测试**：在实际环境中测试延迟和稳定性
4. **文档更新**：更新使用文档和部署指南

---

**更新时间**：2025-07-31  
**状态**：代码已修复，待测试验证  
**推荐方案**：纯RTSP推流（方案B）
