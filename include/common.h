#ifndef COMMON_H
#define COMMON_H

#include <vector>
#include <string>
#include <memory>
#include <chrono>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <thread>
#include <atomic>
#include <iostream>
#include <iomanip>
#include <fstream>
#include <sstream>

#ifdef USE_OPENCV
#include <opencv2/opencv.hpp>
#endif

// 前向声明
class ConfigManager;

// 系统配置管理器
class ConfigManager {
public:
    static ConfigManager& getInstance();
    bool loadConfig(const std::string& configPath = "config/system_config.json");

    // 摄像头配置
    int getCameraCount() const { return cameraCount_; }
    int getCameraWidth() const { return cameraWidth_; }
    int getCameraHeight() const { return cameraHeight_; }
    int getTargetFPS() const { return targetFPS_; }

    // 检测精度要求
    double getMinCrackWidth() const { return minCrackWidth_; }
    double getMinDamageSize() const { return minDamageSize_; }

    // 性能参数
    int getMaxQueueSize() const { return maxQueueSize_; }
    int getProcessTimeout() const { return processTimeout_; }

    // 推流配置
    bool getStreamingEnabled() const { return streamingEnabled_; }
    const std::string& getStreamingMode() const { return streamingMode_; }
    const std::string& getStreamingServerType() const { return streamingServerType_; }
    const std::string& getRtspServerAddress() const { return rtspServerAddress_; }
    int getRtspServerPort() const { return rtspServerPort_; }
    std::string getStreamingPushUrl() const;
    std::string getStreamingViewUrl() const;
    const std::string& getStreamingKey() const { return streamingKey_; }

    // 视频编码配置
    const std::string& getVideoCodec() const { return videoCodec_; }
    int getVideoWidth() const { return videoWidth_; }
    int getVideoHeight() const { return videoHeight_; }
    int getVideoFPS() const { return videoFPS_; }
    int getVideoBitrate() const { return videoBitrate_; }
    const std::string& getVideoPreset() const { return videoPreset_; }
    const std::string& getVideoProfile() const { return videoProfile_; }

    // 连接配置
    int getConnectTimeoutMs() const { return connectTimeoutMs_; }
    int getReconnectIntervalMs() const { return reconnectIntervalMs_; }
    int getMaxRetries() const { return maxRetries_; }

    // 质量控制配置
    bool getAdaptiveBitrate() const { return adaptiveBitrate_; }
    int getMinBitrate() const { return minBitrate_; }
    int getMaxBitrate() const { return maxBitrate_; }
    double getFrameDropThreshold() const { return frameDropThreshold_; }
    int getBufferSize() const { return bufferSize_; }

private:
    ConfigManager() = default;
    bool parseJsonFile(const std::string& filePath);

    // 配置验证方法
    bool validateIpAddress(const std::string& ip) const;
    bool validatePort(int port) const;

    // 摄像头配置
    int cameraCount_ = 4;
    int cameraWidth_ = 640;
    int cameraHeight_ = 480;
    int targetFPS_ = 15;

    // 检测精度要求
    double minCrackWidth_ = 0.1;
    double minDamageSize_ = 10.0;

    // 性能参数
    int maxQueueSize_ = 30;
    int processTimeout_ = 500;

    // 推流配置
    bool streamingEnabled_ = false;
    std::string streamingMode_ = "external_server";
    std::string streamingServerType_ = "mediamtx";
    std::string rtspServerAddress_ = "**************";
    int rtspServerPort_ = 8554;
    std::string streamingKey_ = "";
    std::string rtspPath_ = "/live";

    // 视频编码配置
    std::string videoCodec_ = "h264";
    int videoWidth_ = 640;
    int videoHeight_ = 480;
    int videoFPS_ = 15;
    int videoBitrate_ = 2000000;
    std::string videoPreset_ = "fast";
    std::string videoProfile_ = "baseline";

    // 连接配置
    int connectTimeoutMs_ = 5000;
    int reconnectIntervalMs_ = 5000;
    int maxRetries_ = 3;

    // 质量控制配置
    bool adaptiveBitrate_ = true;
    int minBitrate_ = 500000;
    int maxBitrate_ = 5000000;
    double frameDropThreshold_ = 0.1;
    int bufferSize_ = 1048576;
};

// 系统配置常量 - 现在通过ConfigManager获取
namespace Config {
    // 摄像头配置
    extern int CAMERA_COUNT;           // 摄像头数量
    extern int CAMERA_WIDTH;           // 图像宽度
    extern int CAMERA_HEIGHT;          // 图像高度
    extern int TARGET_FPS;             // 目标帧率

    // 检测精度要求
    extern double MIN_CRACK_WIDTH;     // 最小裂缝宽度(mm)
    extern double MIN_DAMAGE_SIZE;     // 最小块状损伤尺寸(mm)

    // 性能参数
    extern int MAX_QUEUE_SIZE;         // 最大队列长度
    extern int PROCESS_TIMEOUT;        // 处理超时时间(ms)

    // RTSP配置
    extern int RTSP_PORT;              // RTSP端口
    extern std::string RTSP_PATH;      // RTSP路径

    // 初始化配置
    bool initializeConfig(const std::string& configPath = "config/system_config.json");
}

// 损伤类型枚举
enum class DamageType {
    NONE = 0,           // 无损伤
    CRACK = 1,          // 裂缝
    WEAR = 2,           // 磨损
    SCRATCH = 3,        // 刮伤
    PIT = 4,            // 凹坑
    BULGE = 5,          // 鼓包
    AGING = 6,          // 老化
    INSTALL_DAMAGE = 7  // 安装破损
};

// 简化的矩形结构 (当没有OpenCV时使用)
#ifndef USE_OPENCV
struct SimpleRect {
    int x, y, width, height;
    SimpleRect() : x(0), y(0), width(0), height(0) {}
    SimpleRect(int x_, int y_, int w_, int h_) : x(x_), y(y_), width(w_), height(h_) {}
};

struct SimplePoint {
    float x, y;
    SimplePoint() : x(0), y(0) {}
    SimplePoint(float x_, float y_) : x(x_), y(y_) {}
};
#endif

// 损伤信息结构
struct DamageInfo {
    DamageType type;                    // 损伤类型
#ifdef USE_OPENCV
    cv::Rect boundingBox;              // 边界框
    std::vector<cv::Point> contour;    // 轮廓点
    cv::Point2f center;                // 中心点
#else
    SimpleRect boundingBox;            // 边界框
    std::vector<SimplePoint> contour;  // 轮廓点
    SimplePoint center;                // 中心点
#endif
    double area;                       // 面积(像素)
    double length;                     // 长度(像素，主要用于裂缝)
    double width;                      // 宽度(像素，主要用于裂缝)
    double confidence;                 // 置信度
    std::string description;           // 描述信息

    DamageInfo() : type(DamageType::NONE), area(0), length(0),
                   width(0), confidence(0) {}
};

// 简化的图像数据结构 (当没有OpenCV时使用)
#ifndef USE_OPENCV
struct SimpleImage {
    int width, height, channels;
    std::vector<unsigned char> data;
    SimpleImage() : width(0), height(0), channels(0) {}
    bool empty() const { return data.empty(); }
};
#endif

// 检测结果结构
struct DetectionResult {
    int cameraId;                           // 摄像头ID
#ifdef USE_OPENCV
    cv::Mat originalImage;                  // 原始图像
    cv::Mat processedImage;                 // 处理后图像
#else
    SimpleImage originalImage;              // 原始图像
    SimpleImage processedImage;             // 处理后图像
#endif
    std::vector<DamageInfo> damages;        // 检测到的损伤
    std::chrono::system_clock::time_point timestamp; // 时间戳
    double processingTime;                  // 处理时间(ms)
    bool isValid;                          // 结果是否有效

    DetectionResult() : cameraId(-1), processingTime(0), isValid(false) {
        timestamp = std::chrono::system_clock::now();
    }
};

// 摄像头状态枚举
enum class CameraStatus {
    DISCONNECTED = 0,   // 未连接
    CONNECTED = 1,      // 已连接
    CAPTURING = 2,      // 正在采集
    ERROR = 3           // 错误状态
};

// 摄像头信息结构
struct CameraInfo {
    int id;                    // 摄像头ID
    std::string devicePath;    // 设备路径
    CameraStatus status;       // 状态
    int width;                 // 图像宽度
    int height;                // 图像高度
    double fps;                // 实际帧率
    std::string errorMsg;      // 错误信息
    
    CameraInfo() : id(-1), status(CameraStatus::DISCONNECTED), 
                   width(0), height(0), fps(0) {}
};

// 系统状态结构
struct SystemStatus {
    bool isRunning;                        // 系统是否运行
    std::vector<CameraInfo> cameras;       // 摄像头状态
    double avgProcessingTime;              // 平均处理时间
    int totalFramesProcessed;              // 总处理帧数
    int totalDamagesDetected;              // 总检测损伤数
    std::chrono::system_clock::time_point startTime; // 启动时间
    
    SystemStatus() : isRunning(false), avgProcessingTime(0), 
                     totalFramesProcessed(0), totalDamagesDetected(0) {
        cameras.resize(Config::CAMERA_COUNT);
        startTime = std::chrono::system_clock::now();
    }
};

// 线程安全的图像队列
template<typename T>
class ThreadSafeQueue {
private:
    std::queue<T> queue_;
    mutable std::mutex mutex_;
    std::condition_variable condition_;
    size_t maxSize_;

public:
    ThreadSafeQueue(size_t maxSize = Config::MAX_QUEUE_SIZE) : maxSize_(maxSize) {}
    
    void push(const T& item) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (queue_.size() >= maxSize_) {
            queue_.pop(); // 移除最旧的元素
        }
        queue_.push(item);
        condition_.notify_one();
    }
    
    bool pop(T& item, int timeoutMs = -1) {
        std::unique_lock<std::mutex> lock(mutex_);
        if (timeoutMs < 0) {
            condition_.wait(lock, [this] { return !queue_.empty(); });
        } else {
            if (!condition_.wait_for(lock, std::chrono::milliseconds(timeoutMs),
                                   [this] { return !queue_.empty(); })) {
                return false;
            }
        }
        item = queue_.front();
        queue_.pop();
        return true;
    }
    
    size_t size() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return queue_.size();
    }
    
    bool empty() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return queue_.empty();
    }
    
    void clear() {
        std::lock_guard<std::mutex> lock(mutex_);
        std::queue<T> empty;
        queue_.swap(empty);
    }
};

// 工具函数
namespace Utils {
    // 获取当前时间戳字符串
    std::string getCurrentTimeString();
    
    // 损伤类型转字符串
    std::string damageTypeToString(DamageType type);
    
    // 计算两点间距离
#ifdef USE_OPENCV
    double calculateDistance(const cv::Point2f& p1, const cv::Point2f& p2);
#else
    double calculateDistance(const SimplePoint& p1, const SimplePoint& p2);
#endif

    // 像素转毫米 (需要根据实际标定参数调整)
    double pixelToMm(double pixels, double pixelSize = 0.1);

    // 毫米转像素
    double mmToPixel(double mm, double pixelSize = 0.1);

    // 创建输出目录
    bool createDirectory(const std::string& path);

    // 保存图像
#ifdef USE_OPENCV
    bool saveImage(const cv::Mat& image, const std::string& filename);
#else
    bool saveImage(const SimpleImage& image, const std::string& filename);
#endif
    
    // 日志输出
    void logInfo(const std::string& message);
    void logWarning(const std::string& message);
    void logError(const std::string& message);
} // namespace Utils

#endif // COMMON_H
