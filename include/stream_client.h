#ifndef STREAM_CLIENT_H
#define STREAM_CLIENT_H

#include "common.h"
#include "video_encoder.h"
#include <memory>
#include <atomic>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <string>
#include <chrono>

#ifdef USE_FFMPEG
extern "C" {
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/avutil.h>
#include <libavutil/opt.h>
}
#endif

/**
 * @brief 推流客户端配置
 */
struct StreamClientConfig {
    // 基本配置
    bool enabled = false;                       // 是否启用推流
    std::string mode = "external_server";       // 推流模式: external_server
    std::string serverType = "mediamtx";        // 服务器类型: mediamtx, ffmpeg, gstreamer
    
    // 推流地址配置
    std::string pushUrl = "rtmp://localhost:1935/live";     // 推流地址
    std::string viewUrl = "rtsp://localhost:8554/live";     // 观看地址
    std::string streamKey = "";                             // 推流密钥（可选）
    
    // 视频编码参数
    std::string codec = "h264";                 // 编码格式
    int width = 640;                           // 视频宽度
    int height = 480;                          // 视频高度
    int fps = 15;                             // 帧率
    int bitrate = 2000000;                    // 码率 (bps)
    std::string preset = "fast";              // 编码预设
    std::string profile = "baseline";         // 编码配置
    
    // 连接参数
    int connectTimeoutMs = 5000;              // 连接超时时间
    int reconnectIntervalMs = 5000;           // 重连间隔
    int maxRetries = 3;                       // 最大重试次数
    
    // 质量控制
    bool adaptiveBitrate = true;              // 自适应码率
    int minBitrate = 500000;                  // 最小码率
    int maxBitrate = 5000000;                 // 最大码率
    double frameDropThreshold = 0.1;          // 丢帧阈值
    
    // 缓冲参数
    int bufferSize = 1024 * 1024;            // 输出缓冲区大小
    int maxQueueSize = 30;                   // 最大队列长度
};

/**
 * @brief 推流客户端状态
 */
enum class StreamClientStatus {
    DISCONNECTED,   // 未连接
    CONNECTING,     // 连接中
    CONNECTED,      // 已连接
    STREAMING,      // 推流中
    RECONNECTING,   // 重连中
    ERROR          // 错误状态
};

/**
 * @brief 推流统计信息
 */
struct StreamClientStats {
    std::atomic<uint64_t> totalFrames{0};      // 总帧数
    std::atomic<uint64_t> sentFrames{0};       // 已发送帧数
    std::atomic<uint64_t> droppedFrames{0};    // 丢弃帧数
    std::atomic<uint64_t> totalBytes{0};       // 总字节数
    std::atomic<double> currentFPS{0.0};       // 当前帧率
    std::atomic<double> avgBitrate{0.0};       // 平均码率
    std::atomic<int> retryCount{0};            // 重试次数
    
    std::chrono::steady_clock::time_point startTime;  // 开始时间
    std::chrono::steady_clock::time_point lastFrameTime;  // 最后帧时间
    mutable std::mutex statsMutex;
    
    void reset() {
        totalFrames = 0;
        sentFrames = 0;
        droppedFrames = 0;
        totalBytes = 0;
        currentFPS = 0.0;
        avgBitrate = 0.0;
        retryCount = 0;
        startTime = std::chrono::steady_clock::now();
        lastFrameTime = startTime;
    }
};

/**
 * @brief FFmpeg推流客户端
 * 
 * 使用FFmpeg将编码后的视频流推送到外部RTSP/RTMP服务器
 * 支持自动重连、质量控制和性能监控
 */
class StreamClient {
public:
    StreamClient();
    ~StreamClient();
    
    // 禁用拷贝构造和赋值
    StreamClient(const StreamClient&) = delete;
    StreamClient& operator=(const StreamClient&) = delete;
    
    /**
     * @brief 初始化推流客户端
     * @param config 推流配置
     * @return 是否初始化成功
     */
    bool initialize(const StreamClientConfig& config);
    
    /**
     * @brief 连接到推流服务器
     * @return 是否连接成功
     */
    bool connect();
    
    /**
     * @brief 断开连接
     */
    void disconnect();
    
    /**
     * @brief 发送编码后的数据包
     * @param packet 编码数据包
     * @return 是否发送成功
     */
    bool sendPacket(const EncodedPacket& packet);
    
    /**
     * @brief 获取客户端状态
     * @return 当前状态
     */
    StreamClientStatus getStatus() const { return status_; }
    
    /**
     * @brief 获取推流统计信息
     * @return 统计信息结构
     */
    const StreamClientStats& getStats() const { return stats_; }
    
    /**
     * @brief 更新推流配置
     * @param config 新的配置
     * @return 是否更新成功
     */
    bool updateConfig(const StreamClientConfig& config);
    
    /**
     * @brief 获取当前配置
     * @return 当前配置
     */
    const StreamClientConfig& getConfig() const { return config_; }
    
    /**
     * @brief 获取推流URL
     * @return 推流URL
     */
    std::string getPushUrl() const { return config_.pushUrl; }
    
    /**
     * @brief 获取观看URL
     * @return 观看URL
     */
    std::string getViewUrl() const { return config_.viewUrl; }
    
    /**
     * @brief 检查是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const { return initialized_; }
    
    /**
     * @brief 检查是否已连接
     * @return 是否已连接
     */
    bool isConnected() const { 
        return status_ == StreamClientStatus::CONNECTED || 
               status_ == StreamClientStatus::STREAMING; 
    }
    
    /**
     * @brief 检查是否正在推流
     * @return 是否正在推流
     */
    bool isStreaming() const { return status_ == StreamClientStatus::STREAMING; }

private:
#ifdef USE_FFMPEG
    // FFmpeg组件
    AVFormatContext* formatContext_ = nullptr;  // 格式上下文
    AVStream* videoStream_ = nullptr;           // 视频流
    AVCodecContext* codecContext_ = nullptr;    // 编码器上下文
    AVPacket* packet_ = nullptr;                // 数据包
#endif
    
    // 配置和状态
    StreamClientConfig config_;                 // 推流配置
    std::atomic<StreamClientStatus> status_;    // 客户端状态
    std::atomic<bool> initialized_;             // 初始化标志
    std::atomic<bool> shouldStop_;              // 停止标志
    
    // 统计信息
    mutable StreamClientStats stats_;           // 推流统计
    
    // 线程管理
    std::unique_ptr<std::thread> reconnectThread_;  // 重连线程
    std::mutex connectionMutex_;                    // 连接互斥锁
    std::condition_variable reconnectCondition_;    // 重连条件变量
    
    // 性能监控
    std::chrono::steady_clock::time_point lastStatsUpdate_;
    
    /**
     * @brief 初始化FFmpeg组件
     * @return 是否初始化成功
     */
    bool initializeFFmpeg();
    
    /**
     * @brief 配置输出格式
     * @return 是否配置成功
     */
    bool configureOutput();
    
    /**
     * @brief 重连工作线程
     */
    void reconnectWorker();
    
    /**
     * @brief 尝试重新连接
     * @return 是否重连成功
     */
    bool attemptReconnect();
    
    /**
     * @brief 更新统计信息
     */
    void updateStats();
    
    /**
     * @brief 清理FFmpeg资源
     */
    void cleanupFFmpeg();
    
    /**
     * @brief 记录错误信息
     * @param message 错误消息
     * @param errorCode FFmpeg错误码
     */
    void logError(const std::string& message, int errorCode = 0) const;
    
    /**
     * @brief 记录信息
     * @param message 信息消息
     */
    void logInfo(const std::string& message) const;
    
    /**
     * @brief 获取FFmpeg错误字符串
     * @param errorCode 错误码
     * @return 错误描述
     */
    std::string getFFmpegError(int errorCode) const;
};

#endif // STREAM_CLIENT_H
