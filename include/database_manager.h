#pragma once

#include "common.h"
#include <sqlite3.h>
#include <string>
#include <vector>
#include <memory>
#include <mutex>

#ifdef USE_OPENCV
#include <opencv2/opencv.hpp>
#endif

// 前向声明
struct DamageResult;

/**
 * @brief SQLite数据库管理类
 * 
 * 负责管理缺损检测结果的SQLite数据库存储，包括：
 * - 数据库初始化和表创建
 * - 检测结果的插入和查询
 * - 数据库连接管理
 * - 事务处理
 * 
 * 设计原则：
 * - 高内聚：所有数据库操作集中在此类中
 * - 低耦合：通过接口与其他模块交互
 * - 线程安全：支持多线程环境下的数据库操作
 */
class DatabaseManager {
public:
    /**
     * @brief 构造函数
     * @param dbPath 数据库文件路径
     */
    explicit DatabaseManager(const std::string& dbPath = "output/detection_results.db");
    
    /**
     * @brief 析构函数
     */
    ~DatabaseManager();
    
    /**
     * @brief 初始化数据库
     * @return 初始化是否成功
     */
    bool initialize();
    
    /**
     * @brief 关闭数据库连接
     */
    void close();
    
    /**
     * @brief 保存检测结果到数据库
     * @param results 检测结果列表
     * @param cameraId 摄像头ID
     * @return 保存是否成功
     */
    bool saveDetectionResults(const std::vector<DamageResult>& results, int cameraId);
    
    /**
     * @brief 检测会话信息结构
     */
    struct DetectionSession {
        int id;
        int cameraId;
        std::string timestamp;
        int damageCount;
        std::string createdAt;
    };
    
    /**
     * @brief 数据库中的损伤结果结构
     */
    struct DatabaseDamageResult {
        int id;
        int sessionId;
        std::string damageType;
        double confidence;
        double sizeMm;
        double centerX;
        double centerY;
        int bboxX;
        int bboxY;
        int bboxWidth;
        int bboxHeight;
        std::string description;
        std::string timestamp;
    };
    
    /**
     * @brief 查询指定摄像头的最近检测会话
     * @param cameraId 摄像头ID
     * @param limit 返回记录数限制
     * @return 检测会话列表
     */
    std::vector<DetectionSession> getRecentSessions(int cameraId, int limit = 10);
    
    /**
     * @brief 查询指定会话的损伤结果
     * @param sessionId 会话ID
     * @return 损伤结果列表
     */
    std::vector<DatabaseDamageResult> getDamageResults(int sessionId);
    
    /**
     * @brief 查询指定时间范围内的检测统计
     * @param startTime 开始时间 (ISO 8601格式)
     * @param endTime 结束时间 (ISO 8601格式)
     * @param cameraId 摄像头ID (-1表示所有摄像头)
     * @return 统计信息
     */
    struct DetectionStats {
        int totalSessions;
        int totalDamages;
        std::map<std::string, int> damageTypeCounts;
        double averageConfidence;
    };
    
    DetectionStats getDetectionStats(const std::string& startTime, 
                                   const std::string& endTime, 
                                   int cameraId = -1);
    
    /**
     * @brief 清理旧数据
     * @param daysToKeep 保留天数
     * @return 清理是否成功
     */
    bool cleanupOldData(int daysToKeep = 30);
    
    /**
     * @brief 检查数据库连接状态
     * @return 连接是否正常
     */
    bool isConnected() const;

private:
    std::string dbPath_;                    // 数据库文件路径
    sqlite3* db_;                          // SQLite数据库连接
    mutable std::mutex dbMutex_;           // 数据库操作互斥锁
    bool isInitialized_;                   // 初始化状态
    
    /**
     * @brief 创建数据库表
     * @return 创建是否成功
     */
    bool createTables();
    
    /**
     * @brief 执行SQL语句
     * @param sql SQL语句
     * @return 执行是否成功
     */
    bool executeSql(const std::string& sql);
    
    /**
     * @brief 开始事务
     * @return 是否成功
     */
    bool beginTransaction();
    
    /**
     * @brief 提交事务
     * @return 是否成功
     */
    bool commitTransaction();
    
    /**
     * @brief 回滚事务
     * @return 是否成功
     */
    bool rollbackTransaction();
    
    /**
     * @brief 插入检测会话
     * @param cameraId 摄像头ID
     * @param timestamp 时间戳
     * @param damageCount 损伤数量
     * @return 会话ID，失败返回-1
     */
    int insertDetectionSession(int cameraId, const std::string& timestamp, int damageCount);
    
    /**
     * @brief 插入损伤结果
     * @param sessionId 会话ID
     * @param result 损伤结果
     * @return 插入是否成功
     */
    bool insertDamageResult(int sessionId, const DamageResult& result);
    
    /**
     * @brief 将DamageType枚举转换为字符串
     * @param type 损伤类型
     * @return 类型字符串
     */
    std::string damageTypeToString(DamageType type);
    
    /**
     * @brief 获取当前时间的ISO 8601格式字符串
     * @return 时间字符串
     */
    std::string getCurrentTimeString();
    
    /**
     * @brief 记录数据库错误日志
     * @param operation 操作名称
     * @param errorMsg 错误信息
     */
    void logDatabaseError(const std::string& operation, const std::string& errorMsg);
};
