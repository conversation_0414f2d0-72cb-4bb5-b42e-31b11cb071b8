#ifndef DAMAGE_DETECTOR_H
#define DAMAGE_DETECTOR_H

#include "common.h"
#include <opencv2/imgproc.hpp>

/**
 * 裂缝检测器
 * 专门用于检测表面裂缝
 */
class CrackDetector {
public:
    CrackDetector();
    ~CrackDetector();
    
    // 检测裂缝
    std::vector<DamageInfo> detectCracks(const cv::Mat& image);
    
    // 设置检测参数
    void setMinCrackLength(double length) { minCrackLength_ = length; }
    void setMinCrackWidth(double width) { minCrackWidth_ = width; }
    void setCannyThreshold1(double threshold) { cannyThreshold1_ = threshold; }
    void setCannyThreshold2(double threshold) { cannyThreshold2_ = threshold; }
    void setHoughThreshold(int threshold) { houghThreshold_ = threshold; }

private:
    // 检测参数
    double minCrackLength_;
    double minCrackWidth_;
    double cannyThreshold1_;
    double cannyThreshold2_;
    int houghThreshold_;
    
    // 辅助方法
    cv::Mat preprocessForCrack(const cv::Mat& image);
    std::vector<cv::Vec4i> detectLines(const cv::Mat& edges);
    std::vector<std::vector<cv::Point>> connectLines(const std::vector<cv::Vec4i>& lines);
    DamageInfo analyzeCrack(const std::vector<cv::Point>& crack);
    bool isValidCrack(const DamageInfo& crack);
    double calculateCrackWidth(const cv::Mat& image, const std::vector<cv::Point>& crack);
};

/**
 * 块状损伤检测器
 * 检测磨损、刮伤、凹坑、鼓包等块状损伤
 */
class BlockDamageDetector {
public:
    BlockDamageDetector();
    ~BlockDamageDetector();
    
    // 检测块状损伤
    std::vector<DamageInfo> detectBlockDamages(const cv::Mat& image);
    
    // 检测磨损
    std::vector<DamageInfo> detectWear(const cv::Mat& image);
    
    // 检测刮伤
    std::vector<DamageInfo> detectScratches(const cv::Mat& image);
    
    // 检测凹坑
    std::vector<DamageInfo> detectPits(const cv::Mat& image);
    
    // 检测鼓包
    std::vector<DamageInfo> detectBulges(const cv::Mat& image);
    
    // 设置检测参数
    void setMinDamageArea(double area) { minDamageArea_ = area; }
    void setTextureThreshold(double threshold) { textureThreshold_ = threshold; }
    void setMorphKernelSize(int size) { morphKernelSize_ = size; }

private:
    // 检测参数
    double minDamageArea_;
    double textureThreshold_;
    int morphKernelSize_;
    
    // 形态学核
    cv::Mat morphKernel_;
    
    // 辅助方法
    cv::Mat calculateTextureFeatures(const cv::Mat& image);
    cv::Mat detectTextureAnomalies(const cv::Mat& texture);
    std::vector<std::vector<cv::Point>> findDamageContours(const cv::Mat& binary);
    DamageType classifyBlockDamage(const cv::Mat& roi, const std::vector<cv::Point>& contour);
    DamageInfo analyzeBlockDamage(const cv::Mat& image, const std::vector<cv::Point>& contour);
    bool isValidBlockDamage(const DamageInfo& damage);
    
    // 特征计算
    double calculateRoughness(const cv::Mat& roi);
    double calculateCircularity(const std::vector<cv::Point>& contour);
    double calculateAspectRatio(const cv::Rect& boundingRect);
};

/**
 * 老化检测器
 * 检测材料老化现象
 */
class AgingDetector {
public:
    AgingDetector();
    ~AgingDetector();
    
    // 检测老化
    std::vector<DamageInfo> detectAging(const cv::Mat& image);
    
    // 设置检测参数
    void setColorThreshold(double threshold) { colorThreshold_ = threshold; }
    void setTextureThreshold(double threshold) { textureThreshold_ = threshold; }

private:
    // 检测参数
    double colorThreshold_;
    double textureThreshold_;
    
    // 辅助方法
    cv::Mat analyzeColorChanges(const cv::Mat& image);
    cv::Mat analyzeTextureChanges(const cv::Mat& image);
    std::vector<DamageInfo> combineAgingFeatures(const cv::Mat& colorMap, 
                                                const cv::Mat& textureMap);
};

/**
 * 安装破损检测器
 * 检测防护套等安装部件的破损
 */
class InstallationDamageDetector {
public:
    InstallationDamageDetector();
    ~InstallationDamageDetector();
    
    // 检测安装破损
    std::vector<DamageInfo> detectInstallationDamage(const cv::Mat& image);
    
    // 设置检测参数
    void setEdgeThreshold(double threshold) { edgeThreshold_ = threshold; }
    void setShapeThreshold(double threshold) { shapeThreshold_ = threshold; }

private:
    // 检测参数
    double edgeThreshold_;
    double shapeThreshold_;
    
    // 辅助方法
    cv::Mat detectEdgeAnomalies(const cv::Mat& image);
    cv::Mat detectShapeAnomalies(const cv::Mat& image);
    std::vector<DamageInfo> analyzeInstallationDamage(const cv::Mat& edges, 
                                                     const cv::Mat& shapes);
};

/**
 * 综合损伤检测器
 * 整合所有检测算法的主检测器
 */
class DamageDetector {
public:
    DamageDetector();
    ~DamageDetector();
    
    // 初始化检测器
    bool initialize();
    
    // 检测所有类型的损伤
    DetectionResult detectDamages(const cv::Mat& image, int cameraId = 0);
    
    // 检测指定类型的损伤
    std::vector<DamageInfo> detectSpecificDamage(const cv::Mat& image, DamageType type);
    
    // 设置检测参数
    void setDetectionThreshold(DamageType type, double threshold);
    void enableDamageType(DamageType type, bool enable);
    
    // 获取检测统计
    int getTotalDetections() const { return totalDetections_; }
    int getDetectionCount(DamageType type) const;
    double getAverageDetectionTime() const { return avgDetectionTime_; }
    
    // 参数调优
    void autoTuneParameters(const std::vector<cv::Mat>& trainingImages);

private:
    // 各类检测器
    std::unique_ptr<CrackDetector> crackDetector_;
    std::unique_ptr<BlockDamageDetector> blockDamageDetector_;
    std::unique_ptr<AgingDetector> agingDetector_;
    std::unique_ptr<InstallationDamageDetector> installationDamageDetector_;
    
    // 检测开关
    std::map<DamageType, bool> enabledTypes_;
    
    // 检测阈值
    std::map<DamageType, double> detectionThresholds_;
    
    // 统计信息
    std::atomic<int> totalDetections_;
    std::map<DamageType, int> detectionCounts_;
    double avgDetectionTime_;
    
    // 辅助方法
    void updateStatistics(const DetectionResult& result);
    std::vector<DamageInfo> filterDamages(const std::vector<DamageInfo>& damages);
    std::vector<DamageInfo> mergeSimilarDamages(const std::vector<DamageInfo>& damages);
    double calculateSimilarity(const DamageInfo& damage1, const DamageInfo& damage2);
    bool shouldMergeDamages(const DamageInfo& damage1, const DamageInfo& damage2);
};

#endif // DAMAGE_DETECTOR_H
