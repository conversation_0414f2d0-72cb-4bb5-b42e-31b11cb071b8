#ifndef IMAGE_PROCESSOR_H
#define IMAGE_PROCESSOR_H

#include "common.h"
#include <opencv2/imgproc.hpp>

/**
 * 图像预处理器类
 * 负责图像的基础预处理操作
 */
class ImagePreprocessor {
public:
    ImagePreprocessor();
    ~ImagePreprocessor();
    
    // 图像预处理主函数
    cv::Mat preprocess(const cv::Mat& input);
    
    // 图像降噪
    cv::Mat denoise(const cv::Mat& input, int method = 0);
    
    // 图像增强
    cv::Mat enhance(const cv::Mat& input);
    
    // 直方图均衡化
    cv::Mat equalizeHistogram(const cv::Mat& input);
    
    // 对比度增强
    cv::Mat enhanceContrast(const cv::Mat& input, double alpha = 1.5, int beta = 0);
    
    // 锐化处理
    cv::Mat sharpen(const cv::Mat& input);
    
    // 边缘保持滤波
    cv::Mat edgePreservingFilter(const cv::Mat& input);
    
    // 图像归一化
    cv::Mat normalize(const cv::Mat& input);
    
    // 设置预处理参数
    void setDenoiseStrength(double strength) { denoiseStrength_ = strength; }
    void setContrastAlpha(double alpha) { contrastAlpha_ = alpha; }
    void setContrastBeta(int beta) { contrastBeta_ = beta; }
    void setSharpenStrength(double strength) { sharpenStrength_ = strength; }

private:
    // 预处理参数
    double denoiseStrength_;
    double contrastAlpha_;
    int contrastBeta_;
    double sharpenStrength_;
    
    // 滤波核
    cv::Mat sharpenKernel_;
    
    // 初始化滤波核
    void initializeKernels();
};

/**
 * 图像质量评估器
 * 评估图像质量，过滤低质量图像
 */
class ImageQualityAssessor {
public:
    ImageQualityAssessor();
    ~ImageQualityAssessor();
    
    // 评估图像质量
    double assessQuality(const cv::Mat& image);
    
    // 检查图像是否模糊
    bool isBlurry(const cv::Mat& image, double threshold = 100.0);
    
    // 检查图像是否过暗或过亮
    bool isProperExposure(const cv::Mat& image, double minMean = 50.0, double maxMean = 200.0);
    
    // 检查图像是否有足够的对比度
    bool hasSufficientContrast(const cv::Mat& image, double threshold = 30.0);
    
    // 计算图像清晰度 (Laplacian方差)
    double calculateSharpness(const cv::Mat& image);
    
    // 计算图像对比度
    double calculateContrast(const cv::Mat& image);
    
    // 计算图像亮度
    double calculateBrightness(const cv::Mat& image);
    
    // 综合质量评分 (0-100)
    double calculateQualityScore(const cv::Mat& image);

private:
    // 质量评估参数
    double blurThreshold_;
    double minBrightness_;
    double maxBrightness_;
    double contrastThreshold_;
};

/**
 * 图像分割器
 * 将图像分割成网格或感兴趣区域
 */
class ImageSegmenter {
public:
    ImageSegmenter();
    ~ImageSegmenter();
    
    // 网格分割 (5cm x 5cm网格)
    std::vector<cv::Rect> gridSegmentation(const cv::Size& imageSize, 
                                          int gridWidth = 50, int gridHeight = 50);
    
    // 自适应分割
    std::vector<cv::Rect> adaptiveSegmentation(const cv::Mat& image);
    
    // 基于边缘的分割
    std::vector<cv::Rect> edgeBasedSegmentation(const cv::Mat& image);
    
    // 提取感兴趣区域
    std::vector<cv::Mat> extractROIs(const cv::Mat& image, 
                                    const std::vector<cv::Rect>& rois);
    
    // 合并重叠区域
    std::vector<cv::Rect> mergeOverlappingRects(const std::vector<cv::Rect>& rects, 
                                               double overlapThreshold = 0.3);

private:
    // 分割参数
    int minRegionSize_;
    double overlapThreshold_;
    
    // 辅助函数
    double calculateOverlap(const cv::Rect& rect1, const cv::Rect& rect2);
    bool shouldMergeRects(const cv::Rect& rect1, const cv::Rect& rect2);
};

/**
 * 图像处理管道
 * 整合所有图像处理步骤的管道类
 */
class ImageProcessingPipeline {
public:
    ImageProcessingPipeline();
    ~ImageProcessingPipeline();
    
    // 处理单张图像
    bool processImage(const cv::Mat& input, cv::Mat& output, 
                     std::vector<cv::Rect>& rois);
    
    // 批量处理图像
    bool processImages(const std::vector<cv::Mat>& inputs, 
                      std::vector<cv::Mat>& outputs,
                      std::vector<std::vector<cv::Rect>>& rois);
    
    // 设置处理参数
    void setPreprocessingEnabled(bool enabled) { enablePreprocessing_ = enabled; }
    void setQualityCheckEnabled(bool enabled) { enableQualityCheck_ = enabled; }
    void setSegmentationEnabled(bool enabled) { enableSegmentation_ = enabled; }
    
    // 设置质量阈值
    void setQualityThreshold(double threshold) { qualityThreshold_ = threshold; }
    
    // 获取处理统计
    int getProcessedCount() const { return processedCount_; }
    int getRejectedCount() const { return rejectedCount_; }
    double getAverageProcessingTime() const { return avgProcessingTime_; }

private:
    // 处理模块
    std::unique_ptr<ImagePreprocessor> preprocessor_;
    std::unique_ptr<ImageQualityAssessor> qualityAssessor_;
    std::unique_ptr<ImageSegmenter> segmenter_;
    
    // 处理选项
    bool enablePreprocessing_;
    bool enableQualityCheck_;
    bool enableSegmentation_;
    
    // 质量阈值
    double qualityThreshold_;
    
    // 统计信息
    std::atomic<int> processedCount_;
    std::atomic<int> rejectedCount_;
    double avgProcessingTime_;
    
    // 性能计时
    std::chrono::steady_clock::time_point startTime_;
    
    // 辅助函数
    void updateStatistics(double processingTime);
};

#endif // IMAGE_PROCESSOR_H
