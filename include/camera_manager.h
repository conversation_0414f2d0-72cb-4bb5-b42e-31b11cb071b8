#ifndef CAMERA_MANAGER_H
#define CAMERA_MANAGER_H

#include "common.h"
#include <opencv2/videoio.hpp>

/**
 * 摄像头管理器类
 * 负责管理多个摄像头的初始化、采集和状态监控
 */
class CameraManager {
public:
    CameraManager();
    ~CameraManager();
    
    // 初始化所有摄像头
    bool initialize();
    
    // 启动采集
    bool startCapture();
    
    // 停止采集
    void stopCapture();
    
    // 获取指定摄像头的图像
    bool getFrame(int cameraId, cv::Mat& frame);
    
    // 获取所有摄像头的图像
    bool getAllFrames(std::vector<cv::Mat>& frames);
    
    // 获取摄像头状态
    CameraInfo getCameraInfo(int cameraId) const;
    
    // 获取所有摄像头状态
    std::vector<CameraInfo> getAllCameraInfo() const;
    
    // 检查摄像头是否可用
    bool isCameraAvailable(int cameraId) const;
    
    // 重新连接摄像头
    bool reconnectCamera(int cameraId);
    
    // 设置摄像头参数
    bool setCameraParameter(int cameraId, int propId, double value);
    
    // 获取摄像头参数
    double getCameraParameter(int cameraId, int propId) const;
    
    // 获取实际帧率
    double getActualFPS(int cameraId) const;
    
    // 是否正在运行
    bool isRunning() const { return isRunning_; }

private:
    // 摄像头对象
    std::vector<std::unique_ptr<cv::VideoCapture>> cameras_;
    
    // 摄像头信息
    std::vector<CameraInfo> cameraInfos_;
    
    // 运行状态
    std::atomic<bool> isRunning_;
    
    // 互斥锁
    mutable std::mutex mutex_;
    
    // 帧率统计
    std::vector<std::chrono::steady_clock::time_point> lastFrameTime_;
    std::vector<double> actualFPS_;
    
    // 私有方法
    bool initializeCamera(int cameraId);
    void updateCameraStatus(int cameraId, CameraStatus status, const std::string& errorMsg = "");
    void updateFPS(int cameraId);
    std::vector<int> detectAvailableCameras();
    bool testCameraCapture(int cameraId);
    void releaseCamera(int cameraId);
    void releaseAllCameras();
};

/**
 * 摄像头采集线程类
 * 为每个摄像头创建独立的采集线程
 */
class CameraCaptureThread {
public:
    CameraCaptureThread(int cameraId, CameraManager* manager);
    ~CameraCaptureThread();
    
    // 启动采集线程
    bool start();
    
    // 停止采集线程
    void stop();
    
    // 获取最新帧
    bool getLatestFrame(cv::Mat& frame);
    
    // 获取帧队列
    ThreadSafeQueue<cv::Mat>& getFrameQueue() { return frameQueue_; }
    
    // 是否正在运行
    bool isRunning() const { return isRunning_; }
    
    // 获取统计信息
    int getFrameCount() const { return frameCount_; }
    int getDroppedFrames() const { return droppedFrames_; }

private:
    int cameraId_;
    CameraManager* cameraManager_;
    std::unique_ptr<std::thread> captureThread_;
    std::atomic<bool> isRunning_;
    std::atomic<bool> shouldStop_;
    
    // 帧队列
    ThreadSafeQueue<cv::Mat> frameQueue_;
    
    // 统计信息
    std::atomic<int> frameCount_;
    std::atomic<int> droppedFrames_;
    
    // 最新帧
    cv::Mat latestFrame_;
    std::mutex frameMutex_;
    
    // 采集线程函数
    void captureLoop();
};

/**
 * 多摄像头同步管理器
 * 负责多个摄像头的同步采集和帧对齐
 */
class MultiCameraSyncManager {
public:
    MultiCameraSyncManager(CameraManager* manager);
    ~MultiCameraSyncManager();
    
    // 启动同步采集
    bool startSyncCapture();
    
    // 停止同步采集
    void stopSyncCapture();
    
    // 获取同步帧组
    bool getSyncFrames(std::vector<cv::Mat>& frames, int timeoutMs = 100);
    
    // 设置同步容差时间(ms)
    void setSyncTolerance(int toleranceMs) { syncTolerance_ = toleranceMs; }
    
    // 获取同步统计信息
    int getSyncFrameCount() const { return syncFrameCount_; }
    int getMissedSyncCount() const { return missedSyncCount_; }

private:
    CameraManager* cameraManager_;
    std::vector<std::unique_ptr<CameraCaptureThread>> captureThreads_;
    
    std::atomic<bool> isRunning_;
    int syncTolerance_; // 同步容差时间(ms)
    
    // 统计信息
    std::atomic<int> syncFrameCount_;
    std::atomic<int> missedSyncCount_;
    
    // 同步方法
    bool waitForSyncFrames(std::vector<cv::Mat>& frames, int timeoutMs);
    bool isFramesSynced(const std::vector<std::chrono::steady_clock::time_point>& timestamps);
};

#endif // CAMERA_MANAGER_H
