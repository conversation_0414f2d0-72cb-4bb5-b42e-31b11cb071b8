# 构建模式详细说明

## 📋 概述

拉吊索缺损识别系统支持两种构建模式：**Debug模式** 和 **Release模式**。每种模式针对不同的使用场景进行了优化。

## 🛠️ 构建模式切换

### 命令行语法

```bash
./build.sh [选项]
```

### 支持的选项

| 选项 | 简写 | 说明 | 默认 |
|------|------|------|------|
| `--release` | `-r` | Release模式构建 | ✓ |
| `--debug` | `-d` | Debug模式构建 | |
| `--help` | `-h` | 显示帮助信息 | |

### 使用示例

```bash
# Release模式（默认）
./build.sh
./build.sh --release
./build.sh -r

# Debug模式
./build.sh --debug
./build.sh -d

# 查看帮助
./build.sh --help
./build.sh -h
```

## 🔍 Debug模式详解

### 编译选项
- **调试信息**: `-g` (生成调试符号)
- **优化级别**: `-O0` (禁用优化)
- **预处理器**: `-DDEBUG` (启用调试宏)
- **断言检查**: 启用 `assert()` 宏

### 特点
- **文件大小**: 较大 (~2.1MB)
- **编译时间**: 通常较快 (无优化处理)
- **运行速度**: 较慢 (未优化代码)
- **内存使用**: 较高 (包含调试信息)

### 适用场景
1. **开发阶段**: 编写和测试新功能
2. **调试问题**: 使用GDB等调试器
3. **算法验证**: 验证检测算法的正确性
4. **错误定位**: 定位运行时错误和崩溃
5. **单元测试**: 运行详细的测试用例

### 调试工具使用
```bash
# 使用GDB调试
cd build
gdb ./bin/FaultDetectRefactored
(gdb) run --test basic
(gdb) bt  # 查看调用栈
```


### 手动构建
```bash
# Debug模式
cd build
cmake -DCMAKE_BUILD_TYPE=Debug ..
make -j$(nproc)

# Release模式
cd build
cmake -DCMAKE_BUILD_TYPE=Release ..
make -j$(nproc)
```

### 环境变量控制
```bash
# 通过环境变量设置构建类型
export BUILD_TYPE=Debug
./build.sh

export BUILD_TYPE=Release
./build.sh
```
