[INFO] 2025-07-31 14:47:37.973 - === 拉吊索缺损识别系统 - 生产模式 ===
[INFO] 2025-07-31 14:47:37.973 - 版本: 1.0.0
[INFO] 2025-07-31 14:47:37.973 - 开始初始化拉吊索缺损识别系统...
[INFO] 2025-07-31 14:47:37.973 - 配置文件加载成功
[INFO] 2025-07-31 14:47:37.973 - 摄像头数量: 1
[INFO] 2025-07-31 14:47:37.973 - 开始初始化摄像头管理器...
[INFO] 2025-07-31 14:47:37.973 - 开始检测可用摄像头...
[INFO] 2025-07-31 14:47:38.077 - ✓ 发现可访问的摄像头设备: /dev/video0
[INFO] 2025-07-31 14:47:38.077 - 摄像头检测完成，找到 1 个可用摄像头
[INFO] 2025-07-31 14:47:38.077 - 检测到 1 个可用摄像头
[INFO] 2025-07-31 14:47:38.077 - 初始化摄像头 0...
[INFO] 2025-07-31 14:47:38.077 - 摄像头 0 预初始化成功 (延迟打开)
[INFO] 2025-07-31 14:47:38.077 - 摄像头管理器初始化完成
[INFO] 2025-07-31 14:47:38.077 - 启动摄像头采集...
[INFO] 2025-07-31 14:47:38.077 - 正在打开摄像头 0...
[INFO] 2025-07-31 14:47:38.486 - ✓ 摄像头 0 启动成功 (640x480@30.000000fps)
[INFO] 2025-07-31 14:47:38.486 - 摄像头采集启动成功
[INFO] 2025-07-31 14:47:38.486 - 初始化缺损检测引擎...
[INFO] 2025-07-31 14:47:38.489 - 数据库表和索引创建成功
[INFO] 2025-07-31 14:47:38.489 - 数据库初始化成功: output/detection_results.db
[INFO] 2025-07-31 14:47:38.489 - 缺损检测引擎初始化完成
[INFO] 2025-07-31 14:47:38.489 - 初始化视频推流功能...
[INFO] 2025-07-31 14:47:38.489 - 初始化FFmpeg库...
[libx264 @ 0x5970675c6a80] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
[libx264 @ 0x5970675c6a80] profile Constrained Baseline, level 2.2, 4:2:0, 8-bit
[INFO] 2025-07-31 14:47:38.493 - VideoEncoder: 视频编码器初始化成功: h264 640x480@15fps, 2000kbps, preset=fast, profile=baseline
[INFO] 2025-07-31 14:47:38.493 - FFmpeg网络组件初始化完成
[INFO] 2025-07-31 14:47:38.618 - StreamClient: 推流客户端初始化成功: rtmp://localhost:1935/live
[INFO] 2025-07-31 14:47:38.619 - RTSPStreamManager: 推流管理器初始化成功: rtsp://localhost:8554/live
[INFO] 2025-07-31 14:47:38.619 - 推流管理器初始化成功
[INFO] 2025-07-31 14:47:38.619 - 推流地址: rtsp://localhost:8554/live
[INFO] 2025-07-31 14:47:38.619 - StreamClient: 推流客户端连接成功
[INFO] 2025-07-31 14:47:38.619 - RTSPStreamManager: 推流管理器启动成功
[INFO] 2025-07-31 14:47:38.619 - 推流服务启动成功
[INFO] 2025-07-31 14:47:38.619 - 系统初始化完成

=== 系统状态 ===
摄像头 0: 采集中 (0.0 fps) [640x480]
推流状态: 运行中 (0.0 fps, 0.0 kbps)
推流地址: rtsp://localhost:8554/live

[INFO] 2025-07-31 14:47:38.620 - 开始拉吊索缺损检测...
[INFO] 2025-07-31 14:47:38.620 - RTSPStreamManager: 推流工作线程启动
[INFO] 2025-07-31 14:47:38.980 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:38.981 - 检测结果已保存到数据库，会话ID: 117，损伤数量: 2
[INFO] 2025-07-31 14:47:38.981 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[INFO] 2025-07-31 14:47:39.053 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:39.054 - 检测结果已保存到数据库，会话ID: 118，损伤数量: 4
[INFO] 2025-07-31 14:47:39.054 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[INFO] 2025-07-31 14:47:39.125 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:39.126 - 检测结果已保存到数据库，会话ID: 119，损伤数量: 2
[INFO] 2025-07-31 14:47:39.126 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[INFO] 2025-07-31 14:47:39.198 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:39.199 - 检测结果已保存到数据库，会话ID: 120，损伤数量: 3
[INFO] 2025-07-31 14:47:39.199 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[INFO] 2025-07-31 14:47:39.270 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:39.271 - 检测结果已保存到数据库，会话ID: 121，损伤数量: 3
[INFO] 2025-07-31 14:47:39.271 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[INFO] 2025-07-31 14:47:39.343 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:39.344 - 检测结果已保存到数据库，会话ID: 122，损伤数量: 4
[INFO] 2025-07-31 14:47:39.344 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[INFO] 2025-07-31 14:47:39.419 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:39.420 - 检测结果已保存到数据库，会话ID: 123，损伤数量: 2
[INFO] 2025-07-31 14:47:39.420 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[INFO] 2025-07-31 14:47:39.493 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:39.494 - 检测结果已保存到数据库，会话ID: 124，损伤数量: 2
[INFO] 2025-07-31 14:47:39.494 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[INFO] 2025-07-31 14:47:39.566 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:39.567 - 检测结果已保存到数据库，会话ID: 125，损伤数量: 2
[INFO] 2025-07-31 14:47:39.568 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[INFO] 2025-07-31 14:47:39.639 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:39.640 - 检测结果已保存到数据库，会话ID: 126，损伤数量: 3
[INFO] 2025-07-31 14:47:39.640 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[INFO] 2025-07-31 14:47:39.710 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:39.711 - 检测结果已保存到数据库，会话ID: 127，损伤数量: 4
[INFO] 2025-07-31 14:47:39.711 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[INFO] 2025-07-31 14:47:39.784 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:39.785 - 检测结果已保存到数据库，会话ID: 128，损伤数量: 1
[INFO] 2025-07-31 14:47:39.785 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[INFO] 2025-07-31 14:47:39.856 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:39.857 - 检测结果已保存到数据库，会话ID: 129，损伤数量: 2
[INFO] 2025-07-31 14:47:39.857 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[INFO] 2025-07-31 14:47:39.928 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:39.929 - 检测结果已保存到数据库，会话ID: 130，损伤数量: 3
[INFO] 2025-07-31 14:47:39.929 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[INFO] 2025-07-31 14:47:40.001 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:40.002 - 检测结果已保存到数据库，会话ID: 131，损伤数量: 2
[INFO] 2025-07-31 14:47:40.002 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[INFO] 2025-07-31 14:47:40.073 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:40.073 - 检测结果已保存到数据库，会话ID: 132，损伤数量: 3
[INFO] 2025-07-31 14:47:40.074 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[INFO] 2025-07-31 14:47:40.145 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:40.146 - 检测结果已保存到数据库，会话ID: 133，损伤数量: 4
[INFO] 2025-07-31 14:47:40.146 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[INFO] 2025-07-31 14:47:40.219 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:40.220 - 检测结果已保存到数据库，会话ID: 134，损伤数量: 2
[INFO] 2025-07-31 14:47:40.220 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[INFO] 2025-07-31 14:47:40.290 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:40.292 - 检测结果已保存到数据库，会话ID: 135，损伤数量: 2
[INFO] 2025-07-31 14:47:40.292 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[INFO] 2025-07-31 14:47:40.364 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:40.371 - 检测结果已保存到数据库，会话ID: 136，损伤数量: 2
[INFO] 2025-07-31 14:47:40.371 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[INFO] 2025-07-31 14:47:40.442 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:40.443 - 检测结果已保存到数据库，会话ID: 137，损伤数量: 3
[INFO] 2025-07-31 14:47:40.443 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[INFO] 2025-07-31 14:47:40.514 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:40.515 - 检测结果已保存到数据库，会话ID: 138，损伤数量: 2
[INFO] 2025-07-31 14:47:40.515 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[INFO] 2025-07-31 14:47:40.588 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:40.588 - 检测结果已保存到数据库，会话ID: 139，损伤数量: 2
[INFO] 2025-07-31 14:47:40.588 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[INFO] 2025-07-31 14:47:40.659 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:40.659 - 检测结果已保存到数据库，会话ID: 140，损伤数量: 3
[INFO] 2025-07-31 14:47:40.659 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[INFO] 2025-07-31 14:47:40.732 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:40.733 - 检测结果已保存到数据库，会话ID: 141，损伤数量: 2
[INFO] 2025-07-31 14:47:40.733 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[INFO] 2025-07-31 14:47:40.803 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:40.804 - 检测结果已保存到数据库，会话ID: 142，损伤数量: 3
[INFO] 2025-07-31 14:47:40.804 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[INFO] 2025-07-31 14:47:40.875 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:40.876 - 检测结果已保存到数据库，会话ID: 143，损伤数量: 3
[INFO] 2025-07-31 14:47:40.876 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[INFO] 2025-07-31 14:47:40.948 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:40.948 - 检测结果已保存到数据库，会话ID: 144，损伤数量: 2
[INFO] 2025-07-31 14:47:40.948 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[INFO] 2025-07-31 14:47:41.020 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:41.021 - 检测结果已保存到数据库，会话ID: 145，损伤数量: 3
[INFO] 2025-07-31 14:47:41.021 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[INFO] 2025-07-31 14:47:41.091 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:41.092 - 检测结果已保存到数据库，会话ID: 146，损伤数量: 4
[INFO] 2025-07-31 14:47:41.092 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[INFO] 2025-07-31 14:47:41.164 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:41.164 - 检测结果已保存到数据库，会话ID: 147，损伤数量: 2
[INFO] 2025-07-31 14:47:41.164 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[INFO] 2025-07-31 14:47:41.235 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:41.236 - 检测结果已保存到数据库，会话ID: 148，损伤数量: 4
[INFO] 2025-07-31 14:47:41.236 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[INFO] 2025-07-31 14:47:41.307 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:41.308 - 检测结果已保存到数据库，会话ID: 149，损伤数量: 1
[INFO] 2025-07-31 14:47:41.308 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:41.377 - StreamClient: 发送数据包失败 (Broken pipe)
[INFO] 2025-07-31 14:47:41.377 - StreamClient: 检测到网络连接断开，标记为需要重连
[ERROR] 2025-07-31 14:47:41.377 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:41.377 - StreamClient: 尝试重新连接...
[INFO] 2025-07-31 14:47:41.383 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:41.383 - 检测结果已保存到数据库，会话ID: 150，损伤数量: 1
[INFO] 2025-07-31 14:47:41.384 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:41.452 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:41.455 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:41.456 - 检测结果已保存到数据库，会话ID: 151，损伤数量: 2
[INFO] 2025-07-31 14:47:41.456 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[INFO] 2025-07-31 14:47:41.501 - StreamClient: 重连成功
[INFO] 2025-07-31 14:47:41.529 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:41.529 - 检测结果已保存到数据库，会话ID: 152，损伤数量: 3
[INFO] 2025-07-31 14:47:41.529 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:41.599 - StreamClient: 发送数据包失败 (Connection reset by peer)
[INFO] 2025-07-31 14:47:41.599 - StreamClient: 检测到网络连接断开，标记为需要重连
[ERROR] 2025-07-31 14:47:41.599 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:41.600 - StreamClient: 尝试重新连接...
[INFO] 2025-07-31 14:47:41.603 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:41.605 - 检测结果已保存到数据库，会话ID: 153，损伤数量: 1
[INFO] 2025-07-31 14:47:41.605 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:41.674 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:41.677 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:41.677 - 检测结果已保存到数据库，会话ID: 154，损伤数量: 3
[INFO] 2025-07-31 14:47:41.678 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[INFO] 2025-07-31 14:47:41.727 - StreamClient: 重连成功
[INFO] 2025-07-31 14:47:41.749 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:41.750 - 检测结果已保存到数据库，会话ID: 155，损伤数量: 3
[INFO] 2025-07-31 14:47:41.750 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:41.820 - StreamClient: 发送数据包失败 (Connection reset by peer)
[INFO] 2025-07-31 14:47:41.820 - StreamClient: 检测到网络连接断开，标记为需要重连
[ERROR] 2025-07-31 14:47:41.820 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:41.820 - StreamClient: 尝试重新连接...
[INFO] 2025-07-31 14:47:41.824 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:41.824 - 检测结果已保存到数据库，会话ID: 156，损伤数量: 2
[INFO] 2025-07-31 14:47:41.824 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:41.893 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:41.896 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:41.897 - 检测结果已保存到数据库，会话ID: 157，损伤数量: 3
[INFO] 2025-07-31 14:47:41.897 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[INFO] 2025-07-31 14:47:41.946 - StreamClient: 重连成功
[INFO] 2025-07-31 14:47:41.968 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:41.969 - 检测结果已保存到数据库，会话ID: 158，损伤数量: 4
[INFO] 2025-07-31 14:47:41.969 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[ERROR] 2025-07-31 14:47:42.039 - StreamClient: 发送数据包失败 (Broken pipe)
[INFO] 2025-07-31 14:47:42.039 - StreamClient: 检测到网络连接断开，标记为需要重连
[ERROR] 2025-07-31 14:47:42.039 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:42.039 - StreamClient: 尝试重新连接...
[INFO] 2025-07-31 14:47:42.048 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:42.049 - 检测结果已保存到数据库，会话ID: 159，损伤数量: 4
[INFO] 2025-07-31 14:47:42.049 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[ERROR] 2025-07-31 14:47:42.121 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:42.133 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:42.134 - 检测结果已保存到数据库，会话ID: 160，损伤数量: 3
[INFO] 2025-07-31 14:47:42.134 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[INFO] 2025-07-31 14:47:42.166 - StreamClient: 重连成功
[INFO] 2025-07-31 14:47:42.211 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:42.212 - 检测结果已保存到数据库，会话ID: 161，损伤数量: 3
[INFO] 2025-07-31 14:47:42.212 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:42.281 - StreamClient: 发送数据包失败 (Broken pipe)
[INFO] 2025-07-31 14:47:42.281 - StreamClient: 检测到网络连接断开，标记为需要重连
[ERROR] 2025-07-31 14:47:42.282 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:42.283 - StreamClient: 尝试重新连接...
[INFO] 2025-07-31 14:47:42.287 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:42.287 - 检测结果已保存到数据库，会话ID: 162，损伤数量: 4
[INFO] 2025-07-31 14:47:42.287 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[ERROR] 2025-07-31 14:47:42.357 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:42.360 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:42.360 - 检测结果已保存到数据库，会话ID: 163，损伤数量: 1
[INFO] 2025-07-31 14:47:42.360 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[INFO] 2025-07-31 14:47:42.406 - StreamClient: 重连成功
[INFO] 2025-07-31 14:47:42.435 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:42.436 - 检测结果已保存到数据库，会话ID: 164，损伤数量: 1
[INFO] 2025-07-31 14:47:42.436 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:42.504 - StreamClient: 发送数据包失败 (Broken pipe)
[INFO] 2025-07-31 14:47:42.504 - StreamClient: 检测到网络连接断开，标记为需要重连
[ERROR] 2025-07-31 14:47:42.505 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:42.505 - StreamClient: 尝试重新连接...
[INFO] 2025-07-31 14:47:42.509 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:42.509 - 检测结果已保存到数据库，会话ID: 165，损伤数量: 3
[INFO] 2025-07-31 14:47:42.509 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:42.580 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:42.582 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:42.582 - 检测结果已保存到数据库，会话ID: 166，损伤数量: 2
[INFO] 2025-07-31 14:47:42.582 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[INFO] 2025-07-31 14:47:42.629 - StreamClient: 重连成功
[INFO] 2025-07-31 14:47:42.654 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:42.655 - 检测结果已保存到数据库，会话ID: 167，损伤数量: 2
[INFO] 2025-07-31 14:47:42.655 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:42.724 - StreamClient: 发送数据包失败 (Broken pipe)
[INFO] 2025-07-31 14:47:42.724 - StreamClient: 检测到网络连接断开，标记为需要重连
[INFO] 2025-07-31 14:47:42.724 - StreamClient: 尝试重新连接...
[ERROR] 2025-07-31 14:47:42.728 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:42.734 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:42.736 - 检测结果已保存到数据库，会话ID: 168，损伤数量: 2
[INFO] 2025-07-31 14:47:42.736 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:42.805 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:42.808 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:42.808 - 检测结果已保存到数据库，会话ID: 169，损伤数量: 2
[INFO] 2025-07-31 14:47:42.808 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[INFO] 2025-07-31 14:47:42.853 - StreamClient: 重连成功
[INFO] 2025-07-31 14:47:42.880 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:42.881 - 检测结果已保存到数据库，会话ID: 170，损伤数量: 2
[INFO] 2025-07-31 14:47:42.881 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:42.950 - StreamClient: 发送数据包失败 (Broken pipe)
[INFO] 2025-07-31 14:47:42.950 - StreamClient: 检测到网络连接断开，标记为需要重连
[ERROR] 2025-07-31 14:47:42.950 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:42.951 - StreamClient: 尝试重新连接...
[tcp @ 0x738ae8007880] Connection to tcp://localhost:1935 failed: Connection refused
[rtmp @ 0x738ae8007980] Cannot open connection tcp://localhost:1935
[ERROR] 2025-07-31 14:47:42.952 - StreamClient: 重连时无法打开输出URL (Connection refused)
[INFO] 2025-07-31 14:47:42.952 - StreamClient: 重连失败，将在 5000ms 后重试
[INFO] 2025-07-31 14:47:42.952 - StreamClient: 尝试重新连接...
[tcp @ 0x738ae8007880] Connection to tcp://localhost:1935 failed: Connection refused
[rtmp @ 0x738ae8007200] Cannot open connection tcp://localhost:1935
[ERROR] 2025-07-31 14:47:42.952 - StreamClient: 重连时无法打开输出URL (Connection refused)
[INFO] 2025-07-31 14:47:42.952 - StreamClient: 重连失败，将在 5000ms 后重试
[INFO] 2025-07-31 14:47:42.952 - StreamClient: 尝试重新连接...
[tcp @ 0x738ae8007240] Connection to tcp://localhost:1935 failed: Connection refused
[rtmp @ 0x738ae8007180] Cannot open connection tcp://localhost:1935
[ERROR] 2025-07-31 14:47:42.952 - StreamClient: 重连时无法打开输出URL (Connection refused)
[ERROR] 2025-07-31 14:47:42.952 - StreamClient: 达到最大重试次数，停止重连
[INFO] 2025-07-31 14:47:42.953 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:42.953 - 检测结果已保存到数据库，会话ID: 171，损伤数量: 3
[INFO] 2025-07-31 14:47:42.953 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:43.022 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:43.024 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:43.025 - 检测结果已保存到数据库，会话ID: 172，损伤数量: 1
[INFO] 2025-07-31 14:47:43.025 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:43.094 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:43.098 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:43.099 - 检测结果已保存到数据库，会话ID: 173，损伤数量: 2
[INFO] 2025-07-31 14:47:43.099 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:43.167 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:43.170 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:43.170 - 检测结果已保存到数据库，会话ID: 174，损伤数量: 2
[INFO] 2025-07-31 14:47:43.170 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:43.239 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:43.242 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:43.242 - 检测结果已保存到数据库，会话ID: 175，损伤数量: 3
[INFO] 2025-07-31 14:47:43.242 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:43.311 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:43.314 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:43.315 - 检测结果已保存到数据库，会话ID: 176，损伤数量: 4
[INFO] 2025-07-31 14:47:43.316 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[ERROR] 2025-07-31 14:47:43.385 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:43.388 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:43.388 - 检测结果已保存到数据库，会话ID: 177，损伤数量: 3
[INFO] 2025-07-31 14:47:43.388 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:43.457 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:43.460 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:43.460 - 检测结果已保存到数据库，会话ID: 178，损伤数量: 3
[INFO] 2025-07-31 14:47:43.460 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:43.534 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:43.537 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:43.537 - 检测结果已保存到数据库，会话ID: 179，损伤数量: 2
[INFO] 2025-07-31 14:47:43.537 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:43.605 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:43.609 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:43.609 - 检测结果已保存到数据库，会话ID: 180，损伤数量: 3
[INFO] 2025-07-31 14:47:43.609 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:43.678 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:43.680 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:43.681 - 检测结果已保存到数据库，会话ID: 181，损伤数量: 1
[INFO] 2025-07-31 14:47:43.681 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:43.751 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:43.753 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:43.753 - 检测结果已保存到数据库，会话ID: 182，损伤数量: 2
[INFO] 2025-07-31 14:47:43.754 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:43.823 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:43.828 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:43.828 - 检测结果已保存到数据库，会话ID: 183，损伤数量: 2
[INFO] 2025-07-31 14:47:43.828 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:43.899 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:43.900 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:43.900 - 检测结果已保存到数据库，会话ID: 184，损伤数量: 1
[INFO] 2025-07-31 14:47:43.900 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:43.970 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:43.972 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:43.973 - 检测结果已保存到数据库，会话ID: 185，损伤数量: 1
[INFO] 2025-07-31 14:47:43.973 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:44.041 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:44.044 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:44.044 - 检测结果已保存到数据库，会话ID: 186，损伤数量: 2
[INFO] 2025-07-31 14:47:44.044 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:44.113 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:44.117 - 摄像头 0 检测到 5 个缺损
[INFO] 2025-07-31 14:47:44.118 - 检测结果已保存到数据库，会话ID: 187，损伤数量: 5
[INFO] 2025-07-31 14:47:44.118 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 5
[ERROR] 2025-07-31 14:47:44.187 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:44.189 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:44.189 - 检测结果已保存到数据库，会话ID: 188，损伤数量: 1
[INFO] 2025-07-31 14:47:44.189 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:44.259 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:44.264 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:44.264 - 检测结果已保存到数据库，会话ID: 189，损伤数量: 2
[INFO] 2025-07-31 14:47:44.265 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:44.335 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:44.339 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:44.340 - 检测结果已保存到数据库，会话ID: 190，损伤数量: 3
[INFO] 2025-07-31 14:47:44.340 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:44.408 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:44.412 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:44.412 - 检测结果已保存到数据库，会话ID: 191，损伤数量: 3
[INFO] 2025-07-31 14:47:44.413 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:44.484 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:44.490 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:44.491 - 检测结果已保存到数据库，会话ID: 192，损伤数量: 3
[INFO] 2025-07-31 14:47:44.491 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:44.564 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:44.568 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:44.568 - 检测结果已保存到数据库，会话ID: 193，损伤数量: 2
[INFO] 2025-07-31 14:47:44.568 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:44.637 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:44.644 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:44.645 - 检测结果已保存到数据库，会话ID: 194，损伤数量: 3
[INFO] 2025-07-31 14:47:44.645 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:44.714 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:44.717 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:44.719 - 检测结果已保存到数据库，会话ID: 195，损伤数量: 4
[INFO] 2025-07-31 14:47:44.719 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[ERROR] 2025-07-31 14:47:44.788 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:44.790 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:44.791 - 检测结果已保存到数据库，会话ID: 196，损伤数量: 4
[INFO] 2025-07-31 14:47:44.791 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[ERROR] 2025-07-31 14:47:44.860 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:44.863 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:44.864 - 检测结果已保存到数据库，会话ID: 197，损伤数量: 3
[INFO] 2025-07-31 14:47:44.864 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] [INFO] 2025-07-31 14:47:44.934 - 摄像头 0 检测到 2 个缺损
2025-07-31 14:47:44.935 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:44.935 - 检测结果已保存到数据库，会话ID: 198，损伤数量: 2
[INFO] 2025-07-31 14:47:44.935 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:45.004 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:45.007 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:45.008 - 检测结果已保存到数据库，会话ID: 199，损伤数量: 2
[INFO] 2025-07-31 14:47:45.008 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:45.077 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:45.083 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:45.085 - 检测结果已保存到数据库，会话ID: 200，损伤数量: 1
[INFO] 2025-07-31 14:47:45.085 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:45.154 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:45.157 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:45.157 - 检测结果已保存到数据库，会话ID: 201，损伤数量: 2
[INFO] 2025-07-31 14:47:45.157 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:45.226 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:45.229 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:45.230 - 检测结果已保存到数据库，会话ID: 202，损伤数量: 3
[INFO] 2025-07-31 14:47:45.230 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:45.299 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:45.305 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:45.305 - 检测结果已保存到数据库，会话ID: 203，损伤数量: 2
[INFO] 2025-07-31 14:47:45.305 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:45.374 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:45.375 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:45.376 - 检测结果已保存到数据库，会话ID: 204，损伤数量: 1
[INFO] 2025-07-31 14:47:45.376 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:45.446 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:45.450 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:45.451 - 检测结果已保存到数据库，会话ID: 205，损伤数量: 1
[INFO] 2025-07-31 14:47:45.451 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:45.520 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:45.521 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:45.521 - 检测结果已保存到数据库，会话ID: 206，损伤数量: 2
[INFO] 2025-07-31 14:47:45.521 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:45.591 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:45.594 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:45.595 - 检测结果已保存到数据库，会话ID: 207，损伤数量: 3
[INFO] 2025-07-31 14:47:45.595 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:45.668 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:45.668 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:45.669 - 检测结果已保存到数据库，会话ID: 208，损伤数量: 1
[INFO] 2025-07-31 14:47:45.669 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:45.741 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:45.744 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:45.744 - 检测结果已保存到数据库，会话ID: 209，损伤数量: 2
[INFO] 2025-07-31 14:47:45.744 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:45.814 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:45.817 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:45.818 - 检测结果已保存到数据库，会话ID: 210，损伤数量: 2
[INFO] 2025-07-31 14:47:45.818 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:45.887 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:45.890 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:45.890 - 检测结果已保存到数据库，会话ID: 211，损伤数量: 3
[INFO] 2025-07-31 14:47:45.890 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:45.959 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:45.961 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:45.962 - 检测结果已保存到数据库，会话ID: 212，损伤数量: 3
[INFO] 2025-07-31 14:47:45.962 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:46.032 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:46.037 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:46.038 - 检测结果已保存到数据库，会话ID: 213，损伤数量: 1
[INFO] 2025-07-31 14:47:46.038 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:46.107 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:46.110 - 摄像头 0 检测到 5 个缺损
[INFO] 2025-07-31 14:47:46.110 - 检测结果已保存到数据库，会话ID: 214，损伤数量: 5
[INFO] 2025-07-31 14:47:46.110 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 5
[ERROR] 2025-07-31 14:47:46.181 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:46.182 - 摄像头 0 检测到 5 个缺损
[INFO] 2025-07-31 14:47:46.183 - 检测结果已保存到数据库，会话ID: 215，损伤数量: 5
[INFO] 2025-07-31 14:47:46.183 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 5
[ERROR] [INFO] 2025-07-31 14:47:46.253 - 摄像头 0 检测到 3 个缺损
2025-07-31 14:47:46.253 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:46.254 - 检测结果已保存到数据库，会话ID: 216，损伤数量: 3
[INFO] 2025-07-31 14:47:46.254 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:46.322 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:46.322 - VideoEncoder: 码率已调整为: 1600 kbps
[INFO] 2025-07-31 14:47:46.322 - RTSPStreamManager: 由于丢帧率过高(60.396040%)，码率调整为: 1600 kbps
[INFO] 2025-07-31 14:47:46.326 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:46.327 - 检测结果已保存到数据库，会话ID: 217，损伤数量: 2
[INFO] 2025-07-31 14:47:46.327 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:46.398 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:46.398 - VideoEncoder: 码率已调整为: 1280 kbps
[INFO] 2025-07-31 14:47:46.398 - RTSPStreamManager: 由于丢帧率过高(60.784314%)，码率调整为: 1280 kbps
[INFO] 2025-07-31 14:47:46.401 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:46.402 - 检测结果已保存到数据库，会话ID: 218，损伤数量: 4
[INFO] 2025-07-31 14:47:46.402 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[ERROR] 2025-07-31 14:47:46.471 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:46.471 - VideoEncoder: 码率已调整为: 1024 kbps
[INFO] 2025-07-31 14:47:46.471 - RTSPStreamManager: 由于丢帧率过高(61.165049%)，码率调整为: 1024 kbps
[INFO] 2025-07-31 14:47:46.478 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:46.479 - 检测结果已保存到数据库，会话ID: 219，损伤数量: 3
[INFO] 2025-07-31 14:47:46.479 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:46.549 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:46.550 - VideoEncoder: 码率已调整为: 819 kbps
[INFO] 2025-07-31 14:47:46.550 - RTSPStreamManager: 由于丢帧率过高(61.538462%)，码率调整为: 819 kbps
[INFO] 2025-07-31 14:47:46.556 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:46.556 - 检测结果已保存到数据库，会话ID: 220，损伤数量: 3
[INFO] 2025-07-31 14:47:46.556 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:46.625 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:46.625 - VideoEncoder: 码率已调整为: 655 kbps
[INFO] 2025-07-31 14:47:46.625 - RTSPStreamManager: 由于丢帧率过高(61.904762%)，码率调整为: 655 kbps
[INFO] 2025-07-31 14:47:46.629 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:46.629 - 检测结果已保存到数据库，会话ID: 221，损伤数量: 3
[INFO] 2025-07-31 14:47:46.629 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:46.699 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:46.699 - VideoEncoder: 码率已调整为: 524 kbps
[INFO] 2025-07-31 14:47:46.699 - RTSPStreamManager: 由于丢帧率过高(62.264151%)，码率调整为: 524 kbps
[INFO] 2025-07-31 14:47:46.700 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:46.701 - 检测结果已保存到数据库，会话ID: 222，损伤数量: 1
[INFO] 2025-07-31 14:47:46.701 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:46.770 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:46.771 - VideoEncoder: 码率已调整为: 500 kbps
[INFO] 2025-07-31 14:47:46.771 - RTSPStreamManager: 由于丢帧率过高(62.616822%)，码率调整为: 500 kbps
[INFO] 2025-07-31 14:47:46.772 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:46.772 - 检测结果已保存到数据库，会话ID: 223，损伤数量: 1
[INFO] 2025-07-31 14:47:46.772 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:46.841 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:46.848 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:46.848 - 检测结果已保存到数据库，会话ID: 224，损伤数量: 2
[INFO] 2025-07-31 14:47:46.848 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:46.917 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:46.921 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:46.921 - 检测结果已保存到数据库，会话ID: 225，损伤数量: 1
[INFO] 2025-07-31 14:47:46.921 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:46.991 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:46.993 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:46.993 - 检测结果已保存到数据库，会话ID: 226，损伤数量: 1
[INFO] 2025-07-31 14:47:46.993 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:47.063 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:47.065 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:47.066 - 检测结果已保存到数据库，会话ID: 227，损伤数量: 2
[INFO] 2025-07-31 14:47:47.066 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:47.134 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:47.136 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:47.137 - 检测结果已保存到数据库，会话ID: 228，损伤数量: 2
[INFO] 2025-07-31 14:47:47.137 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:47.206 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:47.210 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:47.211 - 检测结果已保存到数据库，会话ID: 229，损伤数量: 2
[INFO] 2025-07-31 14:47:47.211 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:47.283 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:47.283 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:47.284 - 检测结果已保存到数据库，会话ID: 230，损伤数量: 3
[INFO] 2025-07-31 14:47:47.284 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:47.353 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:47.355 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:47.356 - 检测结果已保存到数据库，会话ID: 231，损伤数量: 2
[INFO] 2025-07-31 14:47:47.356 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:47.425 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:47.427 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:47.428 - 检测结果已保存到数据库，会话ID: 232，损伤数量: 1
[INFO] 2025-07-31 14:47:47.428 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:47.498 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:47.499 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:47.500 - 检测结果已保存到数据库，会话ID: 233，损伤数量: 4
[INFO] 2025-07-31 14:47:47.500 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[ERROR] 2025-07-31 14:47:47.568 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:47.572 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:47.573 - 检测结果已保存到数据库，会话ID: 234，损伤数量: 2
[INFO] 2025-07-31 14:47:47.573 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:47.647 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:47.654 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:47.655 - 检测结果已保存到数据库，会话ID: 235，损伤数量: 3
[INFO] 2025-07-31 14:47:47.655 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:47.724 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:47.728 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:47.729 - 检测结果已保存到数据库，会话ID: 236，损伤数量: 3
[INFO] 2025-07-31 14:47:47.729 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:47.798 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:47.803 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:47.803 - 检测结果已保存到数据库，会话ID: 237，损伤数量: 2
[INFO] 2025-07-31 14:47:47.803 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:47.876 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:47.881 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:47.883 - 检测结果已保存到数据库，会话ID: 238，损伤数量: 2
[INFO] 2025-07-31 14:47:47.883 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:47.952 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:47.961 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:47.962 - 检测结果已保存到数据库，会话ID: 239，损伤数量: 2
[INFO] 2025-07-31 14:47:47.962 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:48.032 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:48.034 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:48.035 - 检测结果已保存到数据库，会话ID: 240，损伤数量: 1
[INFO] 2025-07-31 14:47:48.035 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:48.103 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:48.113 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:48.114 - 检测结果已保存到数据库，会话ID: 241，损伤数量: 2
[INFO] 2025-07-31 14:47:48.114 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:48.183 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:48.185 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:48.185 - 检测结果已保存到数据库，会话ID: 242，损伤数量: 3
[INFO] 2025-07-31 14:47:48.186 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:48.254 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:48.257 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:48.258 - 检测结果已保存到数据库，会话ID: 243，损伤数量: 3
[INFO] 2025-07-31 14:47:48.258 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:48.328 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:48.333 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:48.333 - 检测结果已保存到数据库，会话ID: 244，损伤数量: 2
[INFO] 2025-07-31 14:47:48.333 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:48.401 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:48.404 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:48.415 - 检测结果已保存到数据库，会话ID: 245，损伤数量: 3
[INFO] 2025-07-31 14:47:48.415 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:48.484 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:48.486 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:48.486 - 检测结果已保存到数据库，会话ID: 246，损伤数量: 2
[INFO] 2025-07-31 14:47:48.486 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:48.556 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:48.563 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:48.564 - 检测结果已保存到数据库，会话ID: 247，损伤数量: 4
[INFO] 2025-07-31 14:47:48.564 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[ERROR] 2025-07-31 14:47:48.634 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:48.642 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:48.645 - 检测结果已保存到数据库，会话ID: 248，损伤数量: 3
[INFO] 2025-07-31 14:47:48.645 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3

=== 系统状态 ===
摄像头 0: 采集中 (12.8 fps) [640x480]
推流状态: 运行中 (1000.0 fps, 21.4 kbps)
推流地址: rtsp://localhost:8554/live

[INFO] 2025-07-31 14:47:48.645 - 已处理帧数: 132
[ERROR] 2025-07-31 14:47:48.716 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:48.727 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:48.729 - 检测结果已保存到数据库，会话ID: 249，损伤数量: 3
[INFO] 2025-07-31 14:47:48.729 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:48.798 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:48.801 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:48.802 - 检测结果已保存到数据库，会话ID: 250，损伤数量: 2
[INFO] 2025-07-31 14:47:48.802 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:48.871 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:48.874 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:48.874 - 检测结果已保存到数据库，会话ID: 251，损伤数量: 3
[INFO] 2025-07-31 14:47:48.874 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:48.942 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:48.945 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:48.945 - 检测结果已保存到数据库，会话ID: 252，损伤数量: 2
[INFO] 2025-07-31 14:47:48.945 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:49.017 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:49.022 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:49.023 - 检测结果已保存到数据库，会话ID: 253，损伤数量: 3
[INFO] 2025-07-31 14:47:49.023 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:49.094 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:49.094 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:49.095 - 检测结果已保存到数据库，会话ID: 254，损伤数量: 2
[INFO] 2025-07-31 14:47:49.095 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:49.165 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:49.167 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:49.167 - 检测结果已保存到数据库，会话ID: 255，损伤数量: 2
[INFO] 2025-07-31 14:47:49.167 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:49.238 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:49.241 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:49.241 - 检测结果已保存到数据库，会话ID: 256，损伤数量: 1
[INFO] 2025-07-31 14:47:49.241 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:49.310 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:49.313 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:49.314 - 检测结果已保存到数据库，会话ID: 257，损伤数量: 2
[INFO] 2025-07-31 14:47:49.314 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:49.384 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:49.386 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:49.387 - 检测结果已保存到数据库，会话ID: 258，损伤数量: 2
[INFO] 2025-07-31 14:47:49.387 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:49.455 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:49.457 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:49.458 - 检测结果已保存到数据库，会话ID: 259，损伤数量: 2
[INFO] 2025-07-31 14:47:49.458 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:49.527 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:49.534 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:49.535 - 检测结果已保存到数据库，会话ID: 260，损伤数量: 4
[INFO] 2025-07-31 14:47:49.535 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[ERROR] 2025-07-31 14:47:49.605 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:49.613 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:49.614 - 检测结果已保存到数据库，会话ID: 261，损伤数量: 3
[INFO] 2025-07-31 14:47:49.615 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:49.683 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:49.686 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:49.686 - 检测结果已保存到数据库，会话ID: 262，损伤数量: 4
[INFO] 2025-07-31 14:47:49.687 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[ERROR] 2025-07-31 14:47:49.756 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:49.759 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:49.759 - 检测结果已保存到数据库，会话ID: 263，损伤数量: 4
[INFO] 2025-07-31 14:47:49.759 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[ERROR] 2025-07-31 14:47:49.830 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:49.832 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:49.833 - 检测结果已保存到数据库，会话ID: 264，损伤数量: 3
[INFO] 2025-07-31 14:47:49.833 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:49.902 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:49.903 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:49.904 - 检测结果已保存到数据库，会话ID: 265，损伤数量: 3
[INFO] 2025-07-31 14:47:49.904 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:49.973 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:49.975 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:49.975 - 检测结果已保存到数据库，会话ID: 266，损伤数量: 3
[INFO] 2025-07-31 14:47:49.975 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:50.045 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:50.047 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:50.047 - 检测结果已保存到数据库，会话ID: 267，损伤数量: 1
[INFO] 2025-07-31 14:47:50.047 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:50.116 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:50.121 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:50.122 - 检测结果已保存到数据库，会话ID: 268，损伤数量: 2
[INFO] 2025-07-31 14:47:50.122 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:50.195 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:50.204 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:50.205 - 检测结果已保存到数据库，会话ID: 269，损伤数量: 3
[INFO] 2025-07-31 14:47:50.205 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:50.273 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:50.277 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:50.277 - 检测结果已保存到数据库，会话ID: 270，损伤数量: 1
[INFO] 2025-07-31 14:47:50.278 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:50.346 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:50.350 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:50.351 - 检测结果已保存到数据库，会话ID: 271，损伤数量: 4
[INFO] 2025-07-31 14:47:50.351 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[ERROR] 2025-07-31 14:47:50.420 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:50.425 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:50.427 - 检测结果已保存到数据库，会话ID: 272，损伤数量: 4
[INFO] 2025-07-31 14:47:50.427 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[ERROR] 2025-07-31 14:47:50.495 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:50.497 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:50.499 - 检测结果已保存到数据库，会话ID: 273，损伤数量: 2
[INFO] 2025-07-31 14:47:50.499 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:50.569 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:50.572 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:50.573 - 检测结果已保存到数据库，会话ID: 274，损伤数量: 2
[INFO] 2025-07-31 14:47:50.573 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:50.645 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:50.649 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:50.651 - 检测结果已保存到数据库，会话ID: 275，损伤数量: 2
[INFO] 2025-07-31 14:47:50.651 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:50.721 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:50.728 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:50.729 - 检测结果已保存到数据库，会话ID: 276，损伤数量: 1
[INFO] 2025-07-31 14:47:50.729 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:50.798 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:50.802 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:50.802 - 检测结果已保存到数据库，会话ID: 277，损伤数量: 4
[INFO] 2025-07-31 14:47:50.802 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[ERROR] 2025-07-31 14:47:50.871 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:50.873 - 摄像头 0 检测到 6 个缺损
[INFO] 2025-07-31 14:47:50.874 - 检测结果已保存到数据库，会话ID: 278，损伤数量: 6
[INFO] 2025-07-31 14:47:50.874 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 6
[ERROR] 2025-07-31 14:47:50.943 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:50.946 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:50.946 - 检测结果已保存到数据库，会话ID: 279，损伤数量: 4
[INFO] 2025-07-31 14:47:50.946 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[ERROR] 2025-07-31 14:47:51.015 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:51.017 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:51.018 - 检测结果已保存到数据库，会话ID: 280，损伤数量: 2
[INFO] 2025-07-31 14:47:51.018 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:51.089 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:51.089 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:51.090 - 检测结果已保存到数据库，会话ID: 281，损伤数量: 3
[INFO] 2025-07-31 14:47:51.090 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:51.159 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:51.162 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:51.162 - 检测结果已保存到数据库，会话ID: 282，损伤数量: 2
[INFO] 2025-07-31 14:47:51.162 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:51.232 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:51.238 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:51.238 - 检测结果已保存到数据库，会话ID: 283，损伤数量: 3
[INFO] 2025-07-31 14:47:51.238 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:51.307 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:51.308 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:51.309 - 检测结果已保存到数据库，会话ID: 284，损伤数量: 1
[INFO] 2025-07-31 14:47:51.309 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:51.378 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:51.380 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:51.381 - 检测结果已保存到数据库，会话ID: 285，损伤数量: 3
[INFO] 2025-07-31 14:47:51.381 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:51.450 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:51.455 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:51.456 - 检测结果已保存到数据库，会话ID: 286，损伤数量: 3
[INFO] 2025-07-31 14:47:51.456 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:51.527 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:51.537 - 摄像头 0 检测到 6 个缺损
[INFO] 2025-07-31 14:47:51.538 - 检测结果已保存到数据库，会话ID: 287，损伤数量: 6
[INFO] 2025-07-31 14:47:51.538 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 6
[ERROR] 2025-07-31 14:47:51.607 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:51.612 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:51.614 - 检测结果已保存到数据库，会话ID: 288，损伤数量: 1
[INFO] 2025-07-31 14:47:51.614 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:51.682 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:51.685 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:51.686 - 检测结果已保存到数据库，会话ID: 289，损伤数量: 4
[INFO] 2025-07-31 14:47:51.686 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[ERROR] 2025-07-31 14:47:51.755 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:51.759 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:51.760 - 检测结果已保存到数据库，会话ID: 290，损伤数量: 2
[INFO] 2025-07-31 14:47:51.760 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:51.831 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:51.835 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:51.836 - 检测结果已保存到数据库，会话ID: 291，损伤数量: 1
[INFO] 2025-07-31 14:47:51.836 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:51.905 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:51.909 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:51.909 - 检测结果已保存到数据库，会话ID: 292，损伤数量: 2
[INFO] 2025-07-31 14:47:51.909 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:51.978 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:51.985 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:51.986 - 检测结果已保存到数据库，会话ID: 293，损伤数量: 2
[INFO] 2025-07-31 14:47:51.986 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:52.058 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:52.063 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:52.064 - 检测结果已保存到数据库，会话ID: 294，损伤数量: 3
[INFO] 2025-07-31 14:47:52.064 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:52.135 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:52.137 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:52.137 - 检测结果已保存到数据库，会话ID: 295，损伤数量: 2
[INFO] 2025-07-31 14:47:52.137 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:52.206 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:52.209 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:52.209 - 检测结果已保存到数据库，会话ID: 296，损伤数量: 2
[INFO] 2025-07-31 14:47:52.210 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:52.278 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:52.285 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:52.286 - 检测结果已保存到数据库，会话ID: 297，损伤数量: 2
[INFO] 2025-07-31 14:47:52.286 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:52.355 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:52.357 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:52.358 - 检测结果已保存到数据库，会话ID: 298，损伤数量: 2
[INFO] 2025-07-31 14:47:52.358 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:52.426 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:52.429 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:52.430 - 检测结果已保存到数据库，会话ID: 299，损伤数量: 3
[INFO] 2025-07-31 14:47:52.430 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:52.499 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:52.503 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:52.504 - 检测结果已保存到数据库，会话ID: 300，损伤数量: 2
[INFO] 2025-07-31 14:47:52.504 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:52.572 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:52.576 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:52.576 - 检测结果已保存到数据库，会话ID: 301，损伤数量: 2
[INFO] 2025-07-31 14:47:52.576 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:52.645 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:52.650 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:52.650 - 检测结果已保存到数据库，会话ID: 302，损伤数量: 2
[INFO] 2025-07-31 14:47:52.650 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:52.720 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:52.721 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:52.721 - 检测结果已保存到数据库，会话ID: 303，损伤数量: 3
[INFO] 2025-07-31 14:47:52.721 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:52.791 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:52.794 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:52.795 - 检测结果已保存到数据库，会话ID: 304，损伤数量: 2
[INFO] 2025-07-31 14:47:52.795 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:52.865 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:52.867 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:52.867 - 检测结果已保存到数据库，会话ID: 305，损伤数量: 3
[INFO] 2025-07-31 14:47:52.867 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:52.936 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:52.939 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:52.940 - 检测结果已保存到数据库，会话ID: 306，损伤数量: 2
[INFO] 2025-07-31 14:47:52.940 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[INFO] 2025-07-31 14:47:53.010 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:53.011 - 检测结果已保存到数据库，会话ID: 307，损伤数量: 1
[INFO] 2025-07-31 14:47:53.011 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:53.011 - RTSPStreamManager: 处理帧失败
[ERROR] 2025-07-31 14:47:53.080 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:53.083 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:53.084 - 检测结果已保存到数据库，会话ID: 308，损伤数量: 1
[INFO] 2025-07-31 14:47:53.084 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:53.153 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:53.156 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:53.157 - 检测结果已保存到数据库，会话ID: 309，损伤数量: 1
[INFO] 2025-07-31 14:47:53.157 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:53.226 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:53.228 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:53.229 - 检测结果已保存到数据库，会话ID: 310，损伤数量: 2
[INFO] 2025-07-31 14:47:53.229 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:53.298 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:53.301 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:53.302 - 检测结果已保存到数据库，会话ID: 311，损伤数量: 2
[INFO] 2025-07-31 14:47:53.302 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:53.370 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:53.374 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:53.375 - 检测结果已保存到数据库，会话ID: 312，损伤数量: 1
[INFO] 2025-07-31 14:47:53.375 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:53.444 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:53.449 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:53.450 - 检测结果已保存到数据库，会话ID: 313，损伤数量: 3
[INFO] 2025-07-31 14:47:53.450 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:53.519 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:53.529 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:53.531 - 检测结果已保存到数据库，会话ID: 314，损伤数量: 3
[INFO] 2025-07-31 14:47:53.531 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:53.601 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:53.608 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:53.608 - 检测结果已保存到数据库，会话ID: 315，损伤数量: 2
[INFO] 2025-07-31 14:47:53.609 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:53.680 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:53.681 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:53.681 - 检测结果已保存到数据库，会话ID: 316，损伤数量: 2
[INFO] 2025-07-31 14:47:53.682 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:53.750 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:53.755 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:53.756 - 检测结果已保存到数据库，会话ID: 317，损伤数量: 2
[INFO] 2025-07-31 14:47:53.756 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:53.826 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:53.833 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:53.834 - 检测结果已保存到数据库，会话ID: 318，损伤数量: 4
[INFO] 2025-07-31 14:47:53.834 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[INFO] 2025-07-31 14:47:53.904 - 摄像头 0 检测到 1 个缺损
[ERROR] 2025-07-31 14:47:53.904 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:53.905 - 检测结果已保存到数据库，会话ID: 319，损伤数量: 1
[INFO] 2025-07-31 14:47:53.905 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:53.974 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:53.977 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:53.977 - 检测结果已保存到数据库，会话ID: 320，损伤数量: 2
[INFO] 2025-07-31 14:47:53.977 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:54.052 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:54.053 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:54.054 - 检测结果已保存到数据库，会话ID: 321，损伤数量: 2
[INFO] 2025-07-31 14:47:54.054 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:54.124 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:54.127 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:54.129 - 检测结果已保存到数据库，会话ID: 322，损伤数量: 2
[INFO] 2025-07-31 14:47:54.129 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:54.199 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:54.200 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:54.201 - 检测结果已保存到数据库，会话ID: 323，损伤数量: 1
[INFO] 2025-07-31 14:47:54.201 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:54.274 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:54.275 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:54.276 - 检测结果已保存到数据库，会话ID: 324，损伤数量: 1
[INFO] 2025-07-31 14:47:54.276 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:54.344 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:54.350 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:54.350 - 检测结果已保存到数据库，会话ID: 325，损伤数量: 2
[INFO] 2025-07-31 14:47:54.350 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:54.421 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:54.427 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:54.428 - 检测结果已保存到数据库，会话ID: 326，损伤数量: 2
[INFO] 2025-07-31 14:47:54.428 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:54.498 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:54.499 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:54.500 - 检测结果已保存到数据库，会话ID: 327，损伤数量: 3
[INFO] 2025-07-31 14:47:54.500 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:54.569 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:54.575 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:54.576 - 检测结果已保存到数据库，会话ID: 328，损伤数量: 2
[INFO] 2025-07-31 14:47:54.576 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:54.644 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:54.652 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:54.653 - 检测结果已保存到数据库，会话ID: 329，损伤数量: 1
[INFO] 2025-07-31 14:47:54.653 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:54.721 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:54.727 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:54.727 - 检测结果已保存到数据库，会话ID: 330，损伤数量: 2
[INFO] 2025-07-31 14:47:54.727 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:54.796 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:54.802 - 摄像头 0 检测到 5 个缺损
[INFO] 2025-07-31 14:47:54.802 - 检测结果已保存到数据库，会话ID: 331，损伤数量: 5
[INFO] 2025-07-31 14:47:54.803 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 5
[ERROR] 2025-07-31 14:47:54.874 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:54.875 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:54.875 - 检测结果已保存到数据库，会话ID: 332，损伤数量: 1
[INFO] 2025-07-31 14:47:54.875 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:54.945 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:54.948 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:54.948 - 检测结果已保存到数据库，会话ID: 333，损伤数量: 2
[INFO] 2025-07-31 14:47:54.948 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:55.018 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:55.020 - 摄像头 0 检测到 5 个缺损
[INFO] 2025-07-31 14:47:55.021 - 检测结果已保存到数据库，会话ID: 334，损伤数量: 5
[INFO] 2025-07-31 14:47:55.021 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 5
[ERROR] 2025-07-31 14:47:55.090 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:55.095 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:55.095 - 检测结果已保存到数据库，会话ID: 335，损伤数量: 3
[INFO] 2025-07-31 14:47:55.095 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:55.166 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:55.171 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:55.172 - 检测结果已保存到数据库，会话ID: 336，损伤数量: 3
[INFO] 2025-07-31 14:47:55.172 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:55.240 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:55.245 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:55.246 - 检测结果已保存到数据库，会话ID: 337，损伤数量: 3
[INFO] 2025-07-31 14:47:55.246 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:55.315 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:55.317 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:55.318 - 检测结果已保存到数据库，会话ID: 338，损伤数量: 1
[INFO] 2025-07-31 14:47:55.318 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:55.386 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:55.392 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:55.392 - 检测结果已保存到数据库，会话ID: 339，损伤数量: 1
[INFO] 2025-07-31 14:47:55.392 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:55.461 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:55.464 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:55.465 - 检测结果已保存到数据库，会话ID: 340，损伤数量: 1
[INFO] 2025-07-31 14:47:55.465 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:55.535 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:55.535 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:55.536 - 检测结果已保存到数据库，会话ID: 341，损伤数量: 2
[INFO] 2025-07-31 14:47:55.536 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:55.604 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:55.609 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:55.610 - 检测结果已保存到数据库，会话ID: 342，损伤数量: 2
[INFO] 2025-07-31 14:47:55.610 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:55.679 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:55.684 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:55.684 - 检测结果已保存到数据库，会话ID: 343，损伤数量: 2
[INFO] 2025-07-31 14:47:55.684 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:55.754 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:55.761 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:55.762 - 检测结果已保存到数据库，会话ID: 344，损伤数量: 2
[INFO] 2025-07-31 14:47:55.762 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:55.835 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:55.839 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:55.840 - 检测结果已保存到数据库，会话ID: 345，损伤数量: 3
[INFO] 2025-07-31 14:47:55.840 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:55.909 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:55.916 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:55.917 - 检测结果已保存到数据库，会话ID: 346，损伤数量: 1
[INFO] 2025-07-31 14:47:55.917 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:55.986 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:55.993 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:55.993 - 检测结果已保存到数据库，会话ID: 347，损伤数量: 2
[INFO] 2025-07-31 14:47:55.993 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:56.062 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:56.065 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:56.066 - 检测结果已保存到数据库，会话ID: 348，损伤数量: 2
[INFO] 2025-07-31 14:47:56.066 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:56.134 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:56.137 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:56.139 - 检测结果已保存到数据库，会话ID: 349，损伤数量: 1
[INFO] 2025-07-31 14:47:56.140 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:56.208 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:56.211 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:56.212 - 检测结果已保存到数据库，会话ID: 350，损伤数量: 2
[INFO] 2025-07-31 14:47:56.212 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:56.281 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:56.285 - 摄像头 0 检测到 4 个缺损
[INFO] 2025-07-31 14:47:56.285 - 检测结果已保存到数据库，会话ID: 351，损伤数量: 4
[INFO] 2025-07-31 14:47:56.285 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 4
[ERROR] 2025-07-31 14:47:56.355 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:56.359 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:56.360 - 检测结果已保存到数据库，会话ID: 352，损伤数量: 3
[INFO] 2025-07-31 14:47:56.360 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:56.429 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:56.433 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:56.434 - 检测结果已保存到数据库，会话ID: 353，损伤数量: 1
[INFO] 2025-07-31 14:47:56.434 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:56.503 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:56.507 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:56.508 - 检测结果已保存到数据库，会话ID: 354，损伤数量: 2
[INFO] 2025-07-31 14:47:56.509 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:56.581 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:56.586 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:56.587 - 检测结果已保存到数据库，会话ID: 355，损伤数量: 3
[INFO] 2025-07-31 14:47:56.587 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:56.657 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:56.661 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:56.662 - 检测结果已保存到数据库，会话ID: 356，损伤数量: 2
[INFO] 2025-07-31 14:47:56.662 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:56.731 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:56.733 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:56.734 - 检测结果已保存到数据库，会话ID: 357，损伤数量: 3
[INFO] 2025-07-31 14:47:56.734 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:56.804 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:56.807 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:56.807 - 检测结果已保存到数据库，会话ID: 358，损伤数量: 2
[INFO] 2025-07-31 14:47:56.808 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:56.876 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:56.878 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:56.879 - 检测结果已保存到数据库，会话ID: 359，损伤数量: 2
[INFO] 2025-07-31 14:47:56.879 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:56.948 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:56.951 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:56.952 - 检测结果已保存到数据库，会话ID: 360，损伤数量: 1
[INFO] 2025-07-31 14:47:56.952 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:57.020 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:57.023 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:57.024 - 检测结果已保存到数据库，会话ID: 361，损伤数量: 1
[INFO] 2025-07-31 14:47:57.024 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:57.093 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:57.100 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:57.100 - 检测结果已保存到数据库，会话ID: 362，损伤数量: 3
[INFO] 2025-07-31 14:47:57.101 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:57.171 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:57.176 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:57.177 - 检测结果已保存到数据库，会话ID: 363，损伤数量: 3
[INFO] 2025-07-31 14:47:57.177 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:57.246 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:57.249 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:57.250 - 检测结果已保存到数据库，会话ID: 364，损伤数量: 2
[INFO] 2025-07-31 14:47:57.250 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:57.319 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:57.321 - 摄像头 0 检测到 5 个缺损
[INFO] 2025-07-31 14:47:57.321 - 检测结果已保存到数据库，会话ID: 365，损伤数量: 5
[INFO] 2025-07-31 14:47:57.322 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 5
[ERROR] 2025-07-31 14:47:57.390 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:57.394 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:57.394 - 检测结果已保存到数据库，会话ID: 366，损伤数量: 1
[INFO] 2025-07-31 14:47:57.394 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:57.464 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:57.465 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:57.466 - 检测结果已保存到数据库，会话ID: 367，损伤数量: 2
[INFO] 2025-07-31 14:47:57.466 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:57.539 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:57.543 - 摄像头 0 检测到 3 个缺损
[INFO] 2025-07-31 14:47:57.544 - 检测结果已保存到数据库，会话ID: 368，损伤数量: 3
[INFO] 2025-07-31 14:47:57.544 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 3
[ERROR] 2025-07-31 14:47:57.615 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:57.620 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:57.620 - 检测结果已保存到数据库，会话ID: 369，损伤数量: 2
[INFO] 2025-07-31 14:47:57.620 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:57.690 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:57.693 - 摄像头 0 检测到 2 个缺损
[INFO] 2025-07-31 14:47:57.693 - 检测结果已保存到数据库，会话ID: 370，损伤数量: 2
[INFO] 2025-07-31 14:47:57.693 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 2
[ERROR] 2025-07-31 14:47:57.762 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:57.767 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:57.768 - 检测结果已保存到数据库，会话ID: 371，损伤数量: 1
[INFO] 2025-07-31 14:47:57.768 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:57.838 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:57.851 - 摄像头 0 检测到 1 个缺损
[INFO] 2025-07-31 14:47:57.851 - 检测结果已保存到数据库，会话ID: 372，损伤数量: 1
[INFO] 2025-07-31 14:47:57.851 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 1
[ERROR] 2025-07-31 14:47:57.920 - RTSPStreamManager: 处理帧失败
[INFO] 2025-07-31 14:47:57.923 - 摄像头 0 检测到 5 个缺损
[INFO] 2025-07-31 14:47:57.924 - 检测结果已保存到数据库，会话ID: 373，损伤数量: 5
[INFO] 2025-07-31 14:47:57.924 - 检测结果已保存到数据库，摄像头ID: 0，损伤数量: 5
[INFO] 2025-07-31 14:47:57.962 - 接收到信号 15，准备退出...
[INFO] 2025-07-31 14:47:57.962 - RTSPStreamManager: 推流工作线程结束
[INFO] 2025-07-31 14:47:57.962 - StreamClient: 推流客户端已断开连接
[INFO] 2025-07-31 14:47:57.962 - RTSPStreamManager: 推流管理器已停止
[INFO] 2025-07-31 14:47:57.962 - 停止摄像头采集...
[INFO] 2025-07-31 14:47:57.962 - 摄像头 0 停止采集
[INFO] 2025-07-31 14:47:57.962 - 摄像头采集已停止
[INFO] 2025-07-31 14:47:57.962 - 停止缺损检测引擎...
[INFO] 2025-07-31 14:47:57.990 - 缺损检测循环结束，总共处理 257 帧
[INFO] 2025-07-31 14:47:57.991 - 系统正常退出
[INFO] 2025-07-31 14:47:57.991 - StreamClient: 推流客户端已断开连接
[libx264 @ 0x5970675c6a80] frame I:8     Avg QP:20.12  size: 22220
[libx264 @ 0x5970675c6a80] frame P:216   Avg QP:22.37  size:  2989
[libx264 @ 0x5970675c6a80] mb I  I16..4: 27.8%  0.0% 72.2%
[libx264 @ 0x5970675c6a80] mb P  I16..4:  1.0%  0.0%  0.0%  P16..4: 63.3%  5.3%  2.4%  0.0%  0.0%    skip:27.9%
[libx264 @ 0x5970675c6a80] coded y,uvDC,uvAC intra: 57.6% 98.2% 86.7% inter: 2.0% 65.7% 10.5%
[libx264 @ 0x5970675c6a80] i16 v,h,dc,p: 10% 18% 10% 62%
[libx264 @ 0x5970675c6a80] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 26% 21% 13%  6%  6% 11%  4%  5%  7%
[libx264 @ 0x5970675c6a80] i8c dc,h,v,p: 61% 13% 14% 12%
[libx264 @ 0x5970675c6a80] ref P L0: 51.6% 48.4%
[libx264 @ 0x5970675c6a80] kb/s:441.06
[INFO] 2025-07-31 14:47:58.001 - 停止摄像头采集...
