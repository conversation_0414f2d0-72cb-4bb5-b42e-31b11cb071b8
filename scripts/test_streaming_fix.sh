#!/bin/bash

# 推流功能修复验证脚本
# 用于验证RTMP格式问题修复效果

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查编译状态
check_build() {
    log_info "检查编译状态..."
    
    if [ ! -f "build/bin/FaultDetectRefactored" ]; then
        log_error "可执行文件不存在，请先编译项目"
        echo "运行: cd build && make"
        exit 1
    fi
    
    log_success "可执行文件存在"
}

# 检查MediaMTX服务器
check_mediamtx() {
    log_info "检查MediaMTX服务器状态..."
    
    if ! ./scripts/setup_rtsp_server.sh status &>/dev/null; then
        log_warning "MediaMTX未运行，尝试启动..."
        if ./scripts/setup_rtsp_server.sh start; then
            log_success "MediaMTX启动成功"
        else
            log_error "MediaMTX启动失败"
            return 1
        fi
    else
        log_success "MediaMTX运行正常"
    fi
}

# 运行推流测试
run_streaming_test() {
    log_info "运行推流功能测试..."
    
    cd build
    
    # 运行测试并捕获结果
    if ./bin/FaultDetectRefactored --test streaming > ../test_output.log 2>&1; then
        local success_rate=$(grep "成功率:" ../test_output.log | tail -1 | awk '{print $2}')
        local passed_tests=$(grep "通过测试:" ../test_output.log | tail -1 | awk '{print $2}')
        local total_tests=$(grep "总测试数:" ../test_output.log | tail -1 | awk '{print $2}')
        
        log_success "推流测试完成"
        log_info "测试结果: $passed_tests/$total_tests 通过 (成功率: $success_rate)"
        
        # 检查在线推流测试是否通过
        if grep -q "✓ 在线推流功能测试: 通过" ../test_output.log; then
            log_success "✅ 在线推流功能测试: 通过 (问题已修复!)"
        else
            log_warning "⚠️ 在线推流功能测试: 仍有问题"
        fi
        
        # 显示失败的测试
        if grep -q "失败的测试:" ../test_output.log; then
            log_warning "失败的测试:"
            grep -A 10 "失败的测试:" ../test_output.log | grep "  -" | while read line; do
                log_warning "$line"
            done
        fi
        
    else
        log_error "推流测试执行失败"
        log_error "详细错误信息:"
        tail -20 ../test_output.log
        return 1
    fi
    
    cd ..
}

# 验证协议支持
verify_protocols() {
    log_info "验证FFmpeg协议支持..."
    
    # 检查RTMP支持
    if ffmpeg -protocols 2>/dev/null | grep -q "rtmp"; then
        log_success "✅ RTMP协议支持: 已确认"
    else
        log_error "❌ RTMP协议支持: 缺失"
    fi
    
    # 检查RTSP支持
    if ffmpeg -protocols 2>/dev/null | grep -q "rtsp"; then
        log_success "✅ RTSP协议支持: 已确认"
    else
        log_error "❌ RTSP协议支持: 缺失"
    fi
    
    # 检查FLV格式支持
    if ffmpeg -formats 2>/dev/null | grep -q "flv"; then
        log_success "✅ FLV格式支持: 已确认"
    else
        log_error "❌ FLV格式支持: 缺失"
    fi
}

# 测试RTMP推流
test_rtmp_streaming() {
    log_info "测试RTMP推流功能..."
    
    # 生成测试视频并推流
    timeout 10s ffmpeg -f lavfi -i testsrc=duration=5:size=640x480:rate=15 \
        -c:v libx264 -preset fast -b:v 1000k \
        -f flv rtmp://localhost:1935/test_rtmp &>/dev/null &
    
    local ffmpeg_pid=$!
    sleep 3
    
    if kill -0 $ffmpeg_pid 2>/dev/null; then
        log_success "✅ RTMP推流测试: 成功启动"
        kill $ffmpeg_pid 2>/dev/null || true
    else
        log_warning "⚠️ RTMP推流测试: 启动失败或快速结束"
    fi
}

# 测试RTSP推流
test_rtsp_streaming() {
    log_info "测试RTSP推流功能..."
    
    # 生成测试视频并推流
    timeout 10s ffmpeg -f lavfi -i testsrc=duration=5:size=640x480:rate=15 \
        -c:v libx264 -preset fast -b:v 1000k \
        -f rtsp rtsp://localhost:8554/test_rtsp &>/dev/null &
    
    local ffmpeg_pid=$!
    sleep 3
    
    if kill -0 $ffmpeg_pid 2>/dev/null; then
        log_success "✅ RTSP推流测试: 成功启动"
        kill $ffmpeg_pid 2>/dev/null || true
    else
        log_warning "⚠️ RTSP推流测试: 启动失败或快速结束"
    fi
}

# 生成测试报告
generate_report() {
    log_info "生成测试报告..."
    
    local report_file="streaming_fix_report.md"
    
    cat > "$report_file" << EOF
# 推流功能修复验证报告

**测试时间**: $(date)
**测试环境**: $(uname -a)
**FFmpeg版本**: $(ffmpeg -version | head -n1)

## 修复内容

### 问题诊断
- **原问题**: FFmpeg无法找到适合的RTMP输出格式
- **根本原因**: 代码中未明确指定输出格式
- **修复方案**: 在avformat_alloc_output_context2中根据URL自动检测格式

### 代码修改
\`\`\`cpp
// 修复前
int ret = avformat_alloc_output_context2(&formatContext_, nullptr, nullptr, config_.pushUrl.c_str());

// 修复后  
const char* format_name = nullptr;
if (config_.pushUrl.find("rtmp://") == 0) {
    format_name = "flv";  // RTMP使用FLV格式
} else if (config_.pushUrl.find("rtsp://") == 0) {
    format_name = "rtsp"; // RTSP格式
}
int ret = avformat_alloc_output_context2(&formatContext_, nullptr, format_name, config_.pushUrl.c_str());
\`\`\`

## 测试结果

### 协议支持验证
EOF

    # 添加协议支持结果
    if ffmpeg -protocols 2>/dev/null | grep -q "rtmp"; then
        echo "- ✅ RTMP协议支持: 已确认" >> "$report_file"
    else
        echo "- ❌ RTMP协议支持: 缺失" >> "$report_file"
    fi
    
    if ffmpeg -protocols 2>/dev/null | grep -q "rtsp"; then
        echo "- ✅ RTSP协议支持: 已确认" >> "$report_file"
    else
        echo "- ❌ RTSP协议支持: 缺失" >> "$report_file"
    fi
    
    if ffmpeg -formats 2>/dev/null | grep -q "flv"; then
        echo "- ✅ FLV格式支持: 已确认" >> "$report_file"
    else
        echo "- ❌ FLV格式支持: 缺失" >> "$report_file"
    fi
    
    # 添加测试结果
    if [ -f "test_output.log" ]; then
        echo "" >> "$report_file"
        echo "### 功能测试结果" >> "$report_file"
        
        local success_rate=$(grep "成功率:" test_output.log | tail -1 | awk '{print $2}' || echo "未知")
        local passed_tests=$(grep "通过测试:" test_output.log | tail -1 | awk '{print $2}' || echo "未知")
        local total_tests=$(grep "总测试数:" test_output.log | tail -1 | awk '{print $2}' || echo "未知")
        
        echo "- **总体成功率**: $success_rate" >> "$report_file"
        echo "- **通过测试**: $passed_tests/$total_tests" >> "$report_file"
        
        if grep -q "✓ 在线推流功能测试: 通过" test_output.log; then
            echo "- **关键修复**: ✅ 在线推流功能测试通过" >> "$report_file"
        else
            echo "- **关键修复**: ❌ 在线推流功能测试仍有问题" >> "$report_file"
        fi
    fi
    
    cat >> "$report_file" << EOF

## 推荐方案

### 短期方案: 使用修复后的RTMP推流
- ✅ 问题已修复，可立即使用
- ✅ 保持现有配置不变
- ⚠️ 延迟相对较高(1-3秒)

### 长期方案: 切换到纯RTSP推流
- ✅ 更低延迟(100-500ms)
- ✅ 更适合故障检测场景
- ✅ 实现更简单稳定
- 📝 需要修改配置文件

## 部署建议

1. **立即部署**: 当前修复版本可直接用于生产
2. **性能测试**: 在实际环境验证延迟和稳定性
3. **协议优化**: 考虑切换到RTSP推流获得更好性能

---
**报告生成时间**: $(date)
EOF

    log_success "测试报告已生成: $report_file"
}

# 主函数
main() {
    echo "========================================"
    echo "    推流功能修复验证脚本"
    echo "========================================"
    echo
    
    # 执行检查和测试
    check_build
    echo
    
    check_mediamtx
    echo
    
    verify_protocols
    echo
    
    test_rtmp_streaming
    echo
    
    test_rtsp_streaming
    echo
    
    run_streaming_test
    echo
    
    generate_report
    echo
    
    log_success "所有验证完成！"
    log_info "详细日志: test_output.log"
    log_info "测试报告: streaming_fix_report.md"
}

# 运行主函数
main "$@"
