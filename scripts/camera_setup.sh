#!/bin/bash

echo "=== 摄像头设备检测和配置脚本 ==="

# 检查用户是否在video组中
if ! groups | grep -q video; then
    echo "警告: 用户不在video组中，正在添加..."
    sudo usermod -a -G video $USER
    echo "已添加用户到video组，请重新登录或运行 'newgrp video' 使权限生效"
    echo "然后重新运行此脚本"
    exit 1
fi

echo "✓ 用户权限检查通过"

# 检查v4l-utils是否安装
if ! command -v v4l2-ctl &> /dev/null; then
    echo "安装v4l-utils工具..."
    sudo apt update && sudo apt install -y v4l-utils
fi

echo "=== 检测摄像头设备 ==="

# 列出所有摄像头设备
echo "物理摄像头设备:"
v4l2-ctl --list-devices 2>/dev/null || echo "无法访问摄像头设备，请检查权限"

echo -e "\n=== 分析设备节点 ==="

# 检查每个video设备
working_cameras=()
for dev in /dev/video*; do
    if [ -c "$dev" ]; then
        echo "检查设备: $dev"
        
        # 获取设备能力
        caps=$(v4l2-ctl -d "$dev" --list-formats-ext 2>/dev/null | head -5)
        
        if echo "$caps" | grep -q "Video Capture" && echo "$caps" | grep -q "Size:"; then
            echo "  ✓ 可用于视频捕获"
            working_cameras+=("$dev")
            
            # 显示支持的格式
            echo "  支持的格式:"
            v4l2-ctl -d "$dev" --list-formats-ext 2>/dev/null | grep -E "(MJPG|YUYV|Size:|fps)" | head -10 | sed 's/^/    /'
        else
            echo "  - 不支持视频捕获（可能是元数据设备）"
        fi
        echo
    fi
done

echo "=== 检测结果 ==="
echo "可用于视频捕获的设备数量: ${#working_cameras[@]}"

if [ ${#working_cameras[@]} -eq 0 ]; then
    echo "❌ 没有找到可用的摄像头设备"
    echo "请检查:"
    echo "1. 摄像头是否正确连接"
    echo "2. 用户是否在video组中"
    echo "3. 设备权限是否正确"
    exit 1
fi

echo "✓ 可用设备:"
for cam in "${working_cameras[@]}"; do
    echo "  $cam"
done

# 更新配置文件
config_file="../config/system_config.json"
if [ -f "$config_file" ]; then
    echo -e "\n=== 更新配置文件 ==="
    
    # 使用sed更新摄像头数量
    camera_count=${#working_cameras[@]}
    sed -i "s/\"count\": [0-9]\+/\"count\": $camera_count/" "$config_file"
    
    echo "✓ 已更新配置文件中的摄像头数量为: $camera_count"
    echo "✓ 配置文件位置: $config_file"
else
    echo "⚠ 配置文件不存在: $config_file"
fi

echo -e "\n=== 建议的测试命令 ==="
echo "1. 重新编译项目:"
echo "   cd build && make"
echo ""
echo "2. 运行项目:"
echo "   cd build && ./bin/FaultDetect"
echo ""
echo "3. 如果权限问题，运行:"
echo "   newgrp video"
echo "   然后重新运行项目"

echo -e "\n=== 脚本执行完成 ==="
