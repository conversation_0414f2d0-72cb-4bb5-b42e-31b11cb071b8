#!/bin/bash

# VLC兼容性测试脚本
# 用于测试MediaMTX RTSP服务器与VLC播放器的兼容性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖工具..."
    
    if ! command -v ffmpeg &> /dev/null; then
        log_error "ffmpeg未安装"
        exit 1
    fi
    
    if ! command -v ffplay &> /dev/null; then
        log_error "ffplay未安装"
        exit 1
    fi
    
    if ! command -v vlc &> /dev/null; then
        log_warning "VLC未安装，将跳过VLC测试"
        VLC_AVAILABLE=false
    else
        VLC_AVAILABLE=true
    fi
    
    log_success "依赖检查完成"
}

# 检查MediaMTX状态
check_mediamtx() {
    log_info "检查MediaMTX服务器状态..."
    
    if ! netstat -tuln | grep -q ":8554"; then
        log_error "MediaMTX RTSP服务器未运行"
        log_info "请先启动MediaMTX服务器: ./scripts/setup_rtsp_server.sh start"
        exit 1
    fi
    
    log_success "MediaMTX服务器正在运行"
}

# 启动测试推流
start_test_stream() {
    local stream_path=$1
    log_info "启动测试推流到路径: $stream_path"
    
    # 检查是否有测试视频文件
    local test_video=""
    if [ -f "/home/<USER>/Documents/TCK第三部分.mp4" ]; then
        test_video="/home/<USER>/Documents/TCK第三部分.mp4"
    else
        # 生成测试视频
        log_info "生成测试视频..."
        ffmpeg -f lavfi -i testsrc=duration=30:size=640x480:rate=15 -f lavfi -i sine=frequency=1000:duration=30 -c:v libx264 -preset fast -b:v 1000k -c:a aac -b:a 128k -y test_video.mp4 2>/dev/null
        test_video="test_video.mp4"
    fi
    
    # 启动推流（后台运行）
    ffmpeg -re -i "$test_video" -c:v libx264 -preset fast -b:v 1000k -s 640x480 -c:a aac -b:a 128k -f rtsp rtsp://localhost:8554/$stream_path > /dev/null 2>&1 &
    FFMPEG_PID=$!
    
    # 等待推流启动
    sleep 3
    
    log_success "测试推流已启动 (PID: $FFMPEG_PID)"
}

# 停止测试推流
stop_test_stream() {
    if [ ! -z "$FFMPEG_PID" ]; then
        log_info "停止测试推流..."
        kill $FFMPEG_PID 2>/dev/null || true
        wait $FFMPEG_PID 2>/dev/null || true
        log_success "测试推流已停止"
    fi
}

# 测试ffplay播放
test_ffplay() {
    local url=$1
    local description=$2
    
    log_info "测试ffplay播放: $description"
    log_info "URL: $url"
    
    # 使用ffplay测试播放（5秒后自动退出）
    timeout 5s ffplay -rtsp_transport tcp -autoexit "$url" > /dev/null 2>&1
    local result=$?
    
    if [ $result -eq 0 ] || [ $result -eq 124 ]; then  # 124是timeout的退出码
        log_success "ffplay播放成功: $description"
        return 0
    else
        log_error "ffplay播放失败: $description"
        return 1
    fi
}

# 测试VLC播放
test_vlc() {
    local url=$1
    local description=$2
    
    if [ "$VLC_AVAILABLE" != "true" ]; then
        log_warning "跳过VLC测试: VLC未安装"
        return 0
    fi
    
    log_info "测试VLC播放: $description"
    log_info "URL: $url"
    
    # 使用VLC测试播放（5秒后自动退出）
    timeout 5s vlc --intf dummy --play-and-exit "$url" > /dev/null 2>&1
    local result=$?
    
    if [ $result -eq 0 ] || [ $result -eq 124 ]; then  # 124是timeout的退出码
        log_success "VLC播放成功: $description"
        return 0
    else
        log_error "VLC播放失败: $description"
        return 1
    fi
}

# 主测试函数
run_compatibility_tests() {
    log_info "=== MediaMTX VLC兼容性测试 ==="
    
    local total_tests=0
    local passed_tests=0
    
    # 测试1: 标准路径（无斜杠结尾）
    log_info "--- 测试1: 标准路径格式 ---"
    start_test_stream "test"
    
    total_tests=$((total_tests + 1))
    if test_ffplay "rtsp://localhost:8554/test" "标准路径 - ffplay"; then
        passed_tests=$((passed_tests + 1))
    fi
    
    total_tests=$((total_tests + 1))
    if test_vlc "rtsp://localhost:8554/test" "标准路径 - VLC"; then
        passed_tests=$((passed_tests + 1))
    fi
    
    stop_test_stream
    sleep 2
    
    # 测试2: 斜杠结尾路径
    log_info "--- 测试2: 斜杠结尾路径格式 ---"
    start_test_stream "test"
    
    total_tests=$((total_tests + 1))
    if test_ffplay "rtsp://localhost:8554/test/" "斜杠结尾 - ffplay"; then
        passed_tests=$((passed_tests + 1))
    fi
    
    total_tests=$((total_tests + 1))
    if test_vlc "rtsp://localhost:8554/test/" "斜杠结尾 - VLC"; then
        passed_tests=$((passed_tests + 1))
    fi
    
    stop_test_stream
    sleep 2
    
    # 测试3: UDP传输协议
    log_info "--- 测试3: UDP传输协议 ---"
    start_test_stream "test"
    
    total_tests=$((total_tests + 1))
    if test_ffplay "rtsp://localhost:8554/test/" "UDP传输 - ffplay"; then
        passed_tests=$((passed_tests + 1))
    fi
    
    if [ "$VLC_AVAILABLE" = "true" ]; then
        total_tests=$((total_tests + 1))
        # VLC使用UDP传输
        timeout 5s vlc --intf dummy --play-and-exit --rtsp-tcp=0 "rtsp://localhost:8554/test/" > /dev/null 2>&1
        local result=$?
        if [ $result -eq 0 ] || [ $result -eq 124 ]; then
            log_success "VLC播放成功: UDP传输"
            passed_tests=$((passed_tests + 1))
        else
            log_error "VLC播放失败: UDP传输"
        fi
    fi
    
    stop_test_stream
    
    # 显示测试结果
    log_info "=== 测试结果汇总 ==="
    log_info "总测试数: $total_tests"
    log_info "通过测试: $passed_tests"
    log_info "成功率: $(( passed_tests * 100 / total_tests ))%"
    
    if [ $passed_tests -eq $total_tests ]; then
        log_success "所有兼容性测试通过！"
        return 0
    else
        log_warning "部分测试失败，请检查配置"
        return 1
    fi
}

# 清理函数
cleanup() {
    log_info "清理测试环境..."
    stop_test_stream
    # 清理生成的测试文件
    rm -f test_video.mp4
    log_success "清理完成"
}

# 设置清理陷阱
trap cleanup EXIT

# 主函数
main() {
    case "${1:-test}" in
        test)
            check_dependencies
            check_mediamtx
            run_compatibility_tests
            ;;
        *)
            echo "用法: $0 [test]"
            echo "  test - 运行VLC兼容性测试"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
