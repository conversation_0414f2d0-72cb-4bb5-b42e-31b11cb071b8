#!/bin/bash

# VLC RTSP调试和修复脚本
# 专门用于诊断和解决VLC播放器的RTSP兼容性问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查VLC版本
check_vlc_version() {
    log_info "检查VLC版本..."
    if command -v vlc &> /dev/null; then
        VLC_VERSION=$(vlc --version 2>&1 | head -n1 | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' || echo "未知")
        log_info "VLC版本: $VLC_VERSION"
        return 0
    else
        log_error "VLC未安装"
        return 1
    fi
}

# 启动测试推流
start_test_stream() {
    log_info "启动测试推流..."
    
    # 停止可能存在的推流
    pkill -f "ffmpeg.*rtsp://localhost:8554" || true
    sleep 2
    
    # 启动新的推流
    ffmpeg -f lavfi -i testsrc=duration=120:size=640x480:rate=15 \
           -f lavfi -i sine=frequency=1000:duration=120 \
           -c:v libx264 -preset fast -b:v 1000k \
           -c:a aac -b:a 128k \
           -f rtsp rtsp://localhost:8554/live > /dev/null 2>&1 &
    
    FFMPEG_PID=$!
    log_success "测试推流已启动 (PID: $FFMPEG_PID)"
    
    # 等待推流建立
    sleep 5
    
    # 验证推流状态
    if tail -n 10 mediamtx.log | grep -q "is publishing to path 'live'"; then
        log_success "推流已成功建立"
        return 0
    else
        log_error "推流建立失败"
        return 1
    fi
}

# 停止测试推流
stop_test_stream() {
    if [ ! -z "$FFMPEG_PID" ]; then
        log_info "停止测试推流..."
        kill $FFMPEG_PID 2>/dev/null || true
        wait $FFMPEG_PID 2>/dev/null || true
        log_success "测试推流已停止"
    fi
}

# 测试不同的VLC播放方式
test_vlc_methods() {
    log_info "=== 测试不同的VLC播放方式 ==="
    
    local test_urls=(
        "rtsp://localhost:8554/live"
        "rtsp://localhost:8554/live/"
        "rtsp://127.0.0.1:8554/live"
        "rtsp://127.0.0.1:8554/live/"
    )
    
    local vlc_options=(
        "--intf dummy --play-and-exit"
        "--intf dummy --play-and-exit --rtsp-tcp"
        "--intf dummy --play-and-exit --rtsp-tcp --network-caching=1000"
        "--intf dummy --play-and-exit --rtsp-tcp --rtsp-frame-buffer-size=500000"
    )
    
    for url in "${test_urls[@]}"; do
        log_info "--- 测试URL: $url ---"
        
        for options in "${vlc_options[@]}"; do
            log_info "测试选项: $options"
            
            # 清理之前的日志
            > vlc_debug.log
            
            # 运行VLC测试
            timeout 10s vlc $options --extraintf logger --verbose 2 --logfile vlc_debug.log "$url" > /dev/null 2>&1
            local result=$?
            
            if [ $result -eq 0 ] || [ $result -eq 124 ]; then
                log_success "VLC播放成功"
                return 0
            else
                log_error "VLC播放失败 (退出码: $result)"
                
                # 显示VLC错误日志的关键部分
                if [ -f vlc_debug.log ]; then
                    log_info "VLC错误日志摘要:"
                    grep -i "error\|failed\|rtsp" vlc_debug.log | tail -5 | while read line; do
                        echo "  $line"
                    done
                fi
            fi
            
            sleep 2
        done
    done
    
    return 1
}

# 分析MediaMTX日志中的VLC连接问题
analyze_mediamtx_logs() {
    log_info "=== 分析MediaMTX日志中的VLC连接问题 ==="
    
    if [ -f mediamtx.log ]; then
        log_info "最近的VLC连接尝试:"
        grep -n "path of a SETUP request must end with a slash" mediamtx.log | tail -5 | while read line; do
            echo "  $line"
        done
        
        log_info "最近的连接状态:"
        tail -20 mediamtx.log | grep -E "(opened|closed|created|destroyed)" | while read line; do
            echo "  $line"
        done
    else
        log_warning "MediaMTX日志文件不存在"
    fi
}

# 尝试修复VLC兼容性的配置调整
try_vlc_compatibility_fix() {
    log_info "=== 尝试VLC兼容性修复 ==="
    
    # 备份当前配置
    cp mediamtx_test.yml mediamtx_test.yml.backup
    
    # 创建VLC兼容性优化配置
    cat > mediamtx_vlc_compat.yml << 'EOF'
# MediaMTX VLC兼容性优化配置

# 日志级别
logLevel: debug
logDestinations: [stdout]

# API配置
api: yes
apiAddress: 127.0.0.1:9997

# 指标配置
metrics: yes
metricsAddress: 127.0.0.1:9998

# RTSP服务器配置 - VLC兼容性优化
rtsp: yes
protocols: [tcp, udp, multicast]
rtspAddress: :8554
rtpAddress: :8000
rtcpAddress: :8001
multicastIPRange: *********/16
multicastRTPPort: 8002
multicastRTCPPort: 8003
encryption: "no"
serverKey: server.key
serverCert: server.crt
authMethods: [basic]

# RTMP服务器配置
rtmp: yes
rtmpAddress: :1935
rtmpEncryption: "no"
rtmpServerKey: server.key
rtmpServerCert: server.crt

# HLS配置
hls: yes
hlsAddress: :8888
hlsEncryption: no
hlsServerKey: server.key
hlsServerCert: server.crt
hlsAlwaysRemux: no
hlsVariant: lowLatency
hlsSegmentCount: 7
hlsSegmentDuration: 1s
hlsPartDuration: 200ms
hlsSegmentMaxSize: 50M

# WebRTC配置
webrtc: yes
webrtcAddress: :8889
webrtcEncryption: no
webrtcServerKey: server.key
webrtcServerCert: server.crt

# SRT配置
srt: yes
srtAddress: :8890

# 路径配置 - VLC兼容性优化
paths:
  # 主要直播路径
  live:
    publishUser: ""
    publishPass: ""
    publishIPs: []
    readUser: ""
    readPass: ""
    readIPs: []
    source: publisher
    sourceOnDemand: no
    runOnInit: ""
    runOnInitRestart: no
    runOnDemand: ""
    runOnDemandRestart: no
    runOnDemandStartTimeout: 10s
    runOnDemandCloseAfter: 10s
    runOnUnDemand: ""
    sourceFingerprint: ""
    sourceOnDemandStartTimeout: 10s
    sourceOnDemandCloseAfter: 10s
    sourceRedirect: ""
    disablePublisherOverride: no
    fallback: ""
    srtPublishPassphrase: ""
    srtReadPassphrase: ""
  
  # 带斜杠的路径别名
  "live/":
    publishUser: ""
    publishPass: ""
    publishIPs: []
    readUser: ""
    readPass: ""
    readIPs: []
    source: publisher
    sourceOnDemand: no
    runOnInit: ""
    runOnInitRestart: no
    runOnDemand: ""
    runOnDemandRestart: no
    runOnDemandStartTimeout: 10s
    runOnDemandCloseAfter: 10s
    runOnUnDemand: ""
    sourceFingerprint: ""
    sourceOnDemandStartTimeout: 10s
    sourceOnDemandCloseAfter: 10s
    sourceRedirect: ""
    disablePublisherOverride: no
    fallback: ""
    srtPublishPassphrase: ""
    srtReadPassphrase: ""
  
  # 所有其他路径
  "~^.*":
    publishUser: ""
    publishPass: ""
    readUser: ""
    readPass: ""
EOF
    
    log_success "VLC兼容性配置已创建"
    
    # 重启MediaMTX使用新配置
    log_info "重启MediaMTX使用VLC兼容性配置..."
    ./scripts/setup_rtsp_server.sh stop > /dev/null 2>&1 || true
    sleep 2
    
    # 使用新配置启动
    ./mediamtx mediamtx_vlc_compat.yml > mediamtx.log 2>&1 &
    MEDIAMTX_PID=$!
    echo $MEDIAMTX_PID > mediamtx.pid
    
    sleep 3
    
    if kill -0 $MEDIAMTX_PID 2>/dev/null; then
        log_success "MediaMTX已使用VLC兼容性配置重启"
        return 0
    else
        log_error "MediaMTX重启失败"
        return 1
    fi
}

# 恢复原始配置
restore_original_config() {
    log_info "恢复原始配置..."
    if [ -f mediamtx_test.yml.backup ]; then
        mv mediamtx_test.yml.backup mediamtx_test.yml
        ./scripts/setup_rtsp_server.sh restart > /dev/null 2>&1
        log_success "原始配置已恢复"
    fi
}

# 清理函数
cleanup() {
    log_info "清理测试环境..."
    stop_test_stream
    # 清理临时文件
    rm -f vlc_debug.log mediamtx_vlc_compat.yml
    log_success "清理完成"
}

# 设置清理陷阱
trap cleanup EXIT

# 主函数
main() {
    log_info "=== VLC RTSP兼容性深度诊断 ==="
    
    # 检查VLC版本
    if ! check_vlc_version; then
        exit 1
    fi
    
    # 启动测试推流
    if ! start_test_stream; then
        log_error "无法建立测试推流，退出"
        exit 1
    fi
    
    # 分析当前问题
    analyze_mediamtx_logs
    
    # 测试当前配置下的VLC播放
    log_info "=== 测试当前配置下的VLC播放 ==="
    if test_vlc_methods; then
        log_success "VLC在当前配置下可以播放"
        exit 0
    fi
    
    # 尝试VLC兼容性修复
    log_info "=== 尝试VLC兼容性修复 ==="
    if try_vlc_compatibility_fix; then
        # 重新启动推流
        stop_test_stream
        start_test_stream
        
        # 测试修复后的配置
        if test_vlc_methods; then
            log_success "VLC兼容性修复成功！"
            log_info "建议使用VLC兼容性配置: mediamtx_vlc_compat.yml"
        else
            log_error "VLC兼容性修复失败"
            restore_original_config
        fi
    else
        log_error "无法应用VLC兼容性修复"
    fi
}

# 运行主函数
main "$@"
