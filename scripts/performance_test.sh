#!/bin/bash

# 推流功能性能测试脚本
# 用于测试不同配置下的编码性能和资源使用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统资源
check_system_resources() {
    log_info "检查系统资源..."
    
    # CPU信息
    local cpu_cores=$(nproc)
    local cpu_info=$(lscpu | grep "Model name" | cut -d':' -f2 | xargs)
    log_info "CPU: $cpu_info ($cpu_cores 核心)"
    
    # 内存信息
    local total_mem=$(free -h | grep "Mem:" | awk '{print $2}')
    local avail_mem=$(free -h | grep "Mem:" | awk '{print $7}')
    log_info "内存: $total_mem 总计, $avail_mem 可用"
    
    # 磁盘空间
    local disk_space=$(df -h . | tail -1 | awk '{print $4}')
    log_info "磁盘可用空间: $disk_space"
    
    # 检查FFmpeg
    if command -v ffmpeg &> /dev/null; then
        local ffmpeg_version=$(ffmpeg -version | head -n1 | cut -d' ' -f3)
        log_info "FFmpeg版本: $ffmpeg_version"
    else
        log_error "FFmpeg未安装"
        return 1
    fi
}

# 编码性能测试
test_encoding_performance() {
    log_info "开始编码性能测试..."
    
    local test_dir="performance_test_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$test_dir"
    
    # 测试配置
    local resolutions=("320x240" "640x480" "1280x720" "1920x1080")
    local framerates=(10 15 30 60)
    local bitrates=(500000 1000000 2000000 5000000)
    
    log_info "测试配置:"
    log_info "  分辨率: ${resolutions[*]}"
    log_info "  帧率: ${framerates[*]}"
    log_info "  码率: ${bitrates[*]}"
    
    # 创建测试结果文件
    local results_file="$test_dir/performance_results.csv"
    echo "分辨率,帧率,码率,编码时间(ms),CPU使用率(%),内存使用(MB),状态" > "$results_file"
    
    # 执行测试
    for res in "${resolutions[@]}"; do
        for fps in "${framerates[@]}"; do
            for bitrate in "${bitrates[@]}"; do
                log_info "测试: ${res}@${fps}fps, ${bitrate}bps"
                
                # 运行单个测试
                local result=$(run_single_encoding_test "$res" "$fps" "$bitrate")
                echo "$result" >> "$results_file"
                
                # 短暂休息以避免系统过载
                sleep 1
            done
        done
    done
    
    log_success "编码性能测试完成，结果保存在: $results_file"
    
    # 生成测试报告
    generate_performance_report "$test_dir"
}

# 运行单个编码测试
run_single_encoding_test() {
    local resolution="$1"
    local fps="$2"
    local bitrate="$3"
    
    local width=$(echo $resolution | cut -d'x' -f1)
    local height=$(echo $resolution | cut -d'x' -f2)
    
    # 记录开始时间和资源使用
    local start_time=$(date +%s%3N)
    local start_cpu=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    local start_mem=$(free -m | grep "Mem:" | awk '{print $3}')
    
    # 创建测试视频（使用FFmpeg生成测试模式）
    local test_video="test_${width}x${height}_${fps}fps.mp4"
    
    # 生成10秒的测试视频
    timeout 30s ffmpeg -f lavfi -i testsrc=duration=10:size=${width}x${height}:rate=${fps} \
        -c:v libx264 -b:v ${bitrate} -preset fast -y "$test_video" &>/dev/null
    
    local encoding_result=$?
    
    # 记录结束时间和资源使用
    local end_time=$(date +%s%3N)
    local end_cpu=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    local end_mem=$(free -m | grep "Mem:" | awk '{print $3}')
    
    # 计算指标
    local encoding_time=$((end_time - start_time))
    local cpu_usage=$(echo "$end_cpu - $start_cpu" | bc 2>/dev/null || echo "0")
    local mem_usage=$((end_mem - start_mem))
    
    # 确定状态
    local status="成功"
    if [ $encoding_result -ne 0 ]; then
        status="失败"
    fi
    
    # 清理测试文件
    rm -f "$test_video"
    
    # 返回结果
    echo "${resolution},${fps},${bitrate},${encoding_time},${cpu_usage},${mem_usage},${status}"
}

# 生成性能报告
generate_performance_report() {
    local test_dir="$1"
    local results_file="$test_dir/performance_results.csv"
    local report_file="$test_dir/performance_report.txt"
    
    log_info "生成性能报告..."
    
    cat > "$report_file" << EOF
# 推流功能性能测试报告

## 测试时间
$(date)

## 系统信息
CPU: $(lscpu | grep "Model name" | cut -d':' -f2 | xargs)
核心数: $(nproc)
内存: $(free -h | grep "Mem:" | awk '{print $2}')
FFmpeg: $(ffmpeg -version | head -n1 | cut -d' ' -f3)

## 测试结果摘要
EOF
    
    # 分析结果
    if [ -f "$results_file" ]; then
        local total_tests=$(tail -n +2 "$results_file" | wc -l)
        local successful_tests=$(tail -n +2 "$results_file" | grep "成功" | wc -l)
        local failed_tests=$(tail -n +2 "$results_file" | grep "失败" | wc -l)
        
        echo "总测试数: $total_tests" >> "$report_file"
        echo "成功测试: $successful_tests" >> "$report_file"
        echo "失败测试: $failed_tests" >> "$report_file"
        echo "成功率: $(echo "scale=2; $successful_tests * 100 / $total_tests" | bc)%" >> "$report_file"
        echo "" >> "$report_file"
        
        # 找出最佳和最差性能
        echo "## 性能分析" >> "$report_file"
        
        # 最快编码时间
        local fastest=$(tail -n +2 "$results_file" | grep "成功" | sort -t',' -k4 -n | head -1)
        if [ -n "$fastest" ]; then
            echo "最快编码: $fastest" >> "$report_file"
        fi
        
        # 最慢编码时间
        local slowest=$(tail -n +2 "$results_file" | grep "成功" | sort -t',' -k4 -nr | head -1)
        if [ -n "$slowest" ]; then
            echo "最慢编码: $slowest" >> "$report_file"
        fi
        
        echo "" >> "$report_file"
        echo "详细结果请查看: $results_file" >> "$report_file"
    fi
    
    log_success "性能报告生成完成: $report_file"
    
    # 显示摘要
    if [ -f "$report_file" ]; then
        log_info "测试摘要:"
        grep -E "(总测试数|成功测试|失败测试|成功率)" "$report_file" | while read line; do
            log_info "  $line"
        done
    fi
}

# 内存泄漏测试
test_memory_leaks() {
    log_info "开始内存泄漏测试..."
    
    if ! command -v valgrind &> /dev/null; then
        log_warning "Valgrind未安装，跳过内存泄漏测试"
        return 0
    fi
    
    local test_dir="memory_test_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$test_dir"
    
    log_info "使用Valgrind检测内存泄漏..."
    
    cd build
    timeout 60s valgrind --tool=memcheck --leak-check=full --show-leak-kinds=all \
        --track-origins=yes --log-file="../$test_dir/valgrind.log" \
        ./bin/FaultDetectRefactored --test streaming &>/dev/null || true
    cd ..
    
    # 分析Valgrind结果
    if [ -f "$test_dir/valgrind.log" ]; then
        local leaks=$(grep "definitely lost" "$test_dir/valgrind.log" | tail -1)
        if echo "$leaks" | grep -q "0 bytes"; then
            log_success "未检测到内存泄漏"
        else
            log_warning "检测到可能的内存泄漏: $leaks"
            log_info "详细信息请查看: $test_dir/valgrind.log"
        fi
    else
        log_warning "Valgrind日志文件未生成"
    fi
}

# 压力测试
stress_test() {
    log_info "开始压力测试..."
    
    local test_duration=300  # 5分钟
    local test_dir="stress_test_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$test_dir"
    
    log_info "运行 $test_duration 秒压力测试..."
    
    # 启动资源监控
    monitor_resources "$test_dir" &
    local monitor_pid=$!
    
    # 运行压力测试
    cd build
    timeout ${test_duration}s ./bin/FaultDetectRefactored --test streaming &>/dev/null || true
    cd ..
    
    # 停止监控
    kill $monitor_pid 2>/dev/null || true
    
    log_success "压力测试完成"
    
    # 分析结果
    analyze_stress_test_results "$test_dir"
}

# 监控系统资源
monitor_resources() {
    local output_dir="$1"
    local log_file="$output_dir/resource_usage.log"
    
    echo "时间,CPU使用率(%),内存使用(MB),磁盘IO" > "$log_file"
    
    while true; do
        local timestamp=$(date '+%H:%M:%S')
        local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
        local mem_usage=$(free -m | grep "Mem:" | awk '{print $3}')
        local disk_io=$(iostat -d 1 1 2>/dev/null | tail -1 | awk '{print $4}' || echo "N/A")
        
        echo "$timestamp,$cpu_usage,$mem_usage,$disk_io" >> "$log_file"
        sleep 5
    done
}

# 分析压力测试结果
analyze_stress_test_results() {
    local test_dir="$1"
    local log_file="$test_dir/resource_usage.log"
    
    if [ -f "$log_file" ]; then
        log_info "压力测试资源使用分析:"
        
        # 计算平均值
        local avg_cpu=$(tail -n +2 "$log_file" | cut -d',' -f2 | awk '{sum+=$1} END {print sum/NR}')
        local max_cpu=$(tail -n +2 "$log_file" | cut -d',' -f2 | sort -n | tail -1)
        local avg_mem=$(tail -n +2 "$log_file" | cut -d',' -f3 | awk '{sum+=$1} END {print sum/NR}')
        local max_mem=$(tail -n +2 "$log_file" | cut -d',' -f3 | sort -n | tail -1)
        
        log_info "  平均CPU使用率: ${avg_cpu}%"
        log_info "  最大CPU使用率: ${max_cpu}%"
        log_info "  平均内存使用: ${avg_mem}MB"
        log_info "  最大内存使用: ${max_mem}MB"
        
        # 检查是否有异常
        if (( $(echo "$max_cpu > 90" | bc -l) )); then
            log_warning "CPU使用率过高"
        fi
        
        if (( max_mem > 1024 )); then
            log_warning "内存使用量较高"
        fi
    fi
}

# 主函数
main() {
    case "${1:-all}" in
        encoding)
            check_system_resources
            test_encoding_performance
            ;;
        memory)
            check_system_resources
            test_memory_leaks
            ;;
        stress)
            check_system_resources
            stress_test
            ;;
        all)
            log_info "=== 推流功能完整性能测试 ==="
            check_system_resources
            test_encoding_performance
            test_memory_leaks
            stress_test
            log_success "=== 所有性能测试完成 ==="
            ;;
        *)
            echo "用法: $0 {encoding|memory|stress|all}"
            echo "  encoding - 编码性能测试"
            echo "  memory   - 内存泄漏测试"
            echo "  stress   - 压力测试"
            echo "  all      - 运行所有测试"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
