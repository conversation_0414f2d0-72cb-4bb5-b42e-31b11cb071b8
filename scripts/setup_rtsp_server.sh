#!/bin/bash

# RTSP服务器设置脚本
# 用于下载和配置MediaMTX服务器进行推流测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测系统架构
detect_architecture() {
    local arch=$(uname -m)
    case $arch in
        x86_64)
            echo "amd64"
            ;;
        aarch64|arm64)
            echo "arm64"
            ;;
        armv7l)
            echo "armv7"
            ;;
        *)
            log_error "不支持的架构: $arch"
            exit 1
            ;;
    esac
}

# 下载MediaMTX
download_mediamtx() {
    local version="v1.5.1"
    local arch=$(detect_architecture)
    local os="linux"
    local filename="mediamtx_${version}_${os}_${arch}.tar.gz"
    local download_url="https://github.com/bluenviron/mediamtx/releases/download/${version}/${filename}"
    
    log_info "下载MediaMTX ${version} for ${os}_${arch}..."
    
    # 创建临时目录
    local temp_dir=$(mktemp -d)
    cd "$temp_dir"
    
    # 下载文件
    if command -v wget &> /dev/null; then
        wget -q --show-progress "$download_url" || {
            log_error "下载失败，请检查网络连接"
            exit 1
        }
    elif command -v curl &> /dev/null; then
        curl -L -o "$filename" "$download_url" || {
            log_error "下载失败，请检查网络连接"
            exit 1
        }
    else
        log_error "需要wget或curl来下载文件"
        exit 1
    fi
    
    # 解压文件
    log_info "解压MediaMTX..."
    tar -xzf "$filename"
    
    # 移动到项目目录
    local project_dir="/home/<USER>/project/fault_detect"
    mv mediamtx "$project_dir/"
    mv mediamtx.yml "$project_dir/"
    
    # 清理临时文件
    cd "$project_dir"
    rm -rf "$temp_dir"
    
    # 设置执行权限
    chmod +x mediamtx
    
    log_success "MediaMTX下载完成"
}

# 配置MediaMTX
configure_mediamtx() {
    log_info "配置MediaMTX..."
    
    # 创建自定义配置文件
    cat > mediamtx_test.yml << 'EOF'
# MediaMTX测试配置文件

# 日志级别
logLevel: info
logDestinations: [stdout]

# API配置
api: yes
apiAddress: 127.0.0.1:9997

# 指标配置
metrics: yes
metricsAddress: 127.0.0.1:9998

# RTSP服务器配置
rtsp: yes
protocols: [udp, multicast, tcp]
rtspAddress: :8554
rtpAddress: :8000
rtcpAddress: :8001
multicastIPRange: *********/16
multicastRTPPort: 8002
multicastRTCPPort: 8003
encryption: "no"
serverKey: server.key
serverCert: server.crt
authMethods: [basic]

# RTMP服务器配置
rtmpAddress: :1935
rtmpEncryption: "no"
rtmpServerKey: server.key
rtmpServerCert: server.crt

# HLS配置
hlsAddress: :8888
hlsEncryption: no
hlsServerKey: server.key
hlsServerCert: server.crt
hlsAlwaysRemux: no
hlsVariant: lowLatency
hlsSegmentCount: 7
hlsSegmentDuration: 1s
hlsPartDuration: 200ms
hlsSegmentMaxSize: 50M

# WebRTC配置
webrtcAddress: :8889
webrtcEncryption: no
webrtcServerKey: server.key
webrtcServerCert: server.crt

# 路径配置
paths:
  # 测试路径
  live:
    # 允许发布
    publishUser: ""
    publishPass: ""
    publishIPs: []
    
    # 允许读取
    readUser: ""
    readPass: ""
    readIPs: []
    
    # 运行时参数
    runOnInit: ""
    runOnInitRestart: no
    runOnDemand: ""
    runOnDemandRestart: no
    runOnDemandStartTimeout: 10s
    runOnDemandCloseAfter: 10s
    runOnUnDemand: ""
    
    # 源配置
    source: publisher
    sourceFingerprint: ""
    sourceOnDemand: no
    sourceOnDemandStartTimeout: 10s
    sourceOnDemandCloseAfter: 10s
    sourceRedirect: ""
    
    # 发布者配置
    disablePublisherOverride: no
    fallback: ""
    
    # SRT配置
    srtPublishPassphrase: ""
    srtReadPassphrase: ""
    
  # 所有其他路径
  "~^.*":
    publishUser: ""
    publishPass: ""
    readUser: ""
    readPass: ""
EOF
    
    log_success "MediaMTX配置完成"
}

# 启动MediaMTX
start_mediamtx() {
    log_info "启动MediaMTX服务器..."
    
    # 检查端口是否被占用
    if netstat -tuln 2>/dev/null | grep -q ":8554 "; then
        log_warning "端口8554已被占用，尝试停止现有服务..."
        pkill -f mediamtx || true
        sleep 2
    fi
    
    if netstat -tuln 2>/dev/null | grep -q ":1935 "; then
        log_warning "端口1935已被占用，尝试停止现有服务..."
        pkill -f mediamtx || true
        sleep 2
    fi
    
    # 启动MediaMTX（后台运行）
    ./mediamtx mediamtx_test.yml > mediamtx.log 2>&1 &
    local mediamtx_pid=$!
    
    # 等待服务启动
    log_info "等待MediaMTX启动..."
    sleep 3
    
    # 检查服务是否正常运行
    if kill -0 $mediamtx_pid 2>/dev/null; then
        log_success "MediaMTX启动成功 (PID: $mediamtx_pid)"
        echo $mediamtx_pid > mediamtx.pid
        
        # 显示服务信息
        log_info "服务信息:"
        log_info "  RTSP端口: 8554"
        log_info "  RTMP端口: 1935"
        log_info "  API端口: 9997"
        log_info "  推流地址: rtmp://localhost:1935/live"
        log_info "  观看地址: rtsp://localhost:8554/live"
        
        return 0
    else
        log_error "MediaMTX启动失败"
        cat mediamtx.log
        return 1
    fi
}

# 停止MediaMTX
stop_mediamtx() {
    log_info "停止MediaMTX服务器..."
    
    if [ -f mediamtx.pid ]; then
        local pid=$(cat mediamtx.pid)
        if kill -0 $pid 2>/dev/null; then
            kill $pid
            sleep 2
            if kill -0 $pid 2>/dev/null; then
                kill -9 $pid
            fi
        fi
        rm -f mediamtx.pid
    fi
    
    # 确保所有mediamtx进程都被停止
    pkill -f mediamtx || true
    
    log_success "MediaMTX已停止"
}

# 检查MediaMTX状态
check_mediamtx() {
    log_info "检查MediaMTX状态..."
    
    if [ -f mediamtx.pid ]; then
        local pid=$(cat mediamtx.pid)
        if kill -0 $pid 2>/dev/null; then
            log_success "MediaMTX正在运行 (PID: $pid)"
            
            # 检查端口
            if netstat -tuln 2>/dev/null | grep -q ":8554 "; then
                log_success "RTSP端口8554正常监听"
            else
                log_warning "RTSP端口8554未监听"
            fi
            
            if netstat -tuln 2>/dev/null | grep -q ":1935 "; then
                log_success "RTMP端口1935正常监听"
            else
                log_warning "RTMP端口1935未监听"
            fi
            
            return 0
        else
            log_warning "MediaMTX进程不存在"
            rm -f mediamtx.pid
            return 1
        fi
    else
        log_warning "MediaMTX未运行"
        return 1
    fi
}

# 主函数
main() {
    case "${1:-setup}" in
        setup)
            log_info "=== MediaMTX RTSP服务器设置 ==="
            
            # 检查是否已存在
            if [ -f mediamtx ]; then
                log_info "MediaMTX已存在，跳过下载"
            else
                download_mediamtx
            fi
            
            configure_mediamtx
            start_mediamtx
            ;;
        start)
            start_mediamtx
            ;;
        stop)
            stop_mediamtx
            ;;
        status)
            check_mediamtx
            ;;
        restart)
            stop_mediamtx
            sleep 1
            start_mediamtx
            ;;
        *)
            echo "用法: $0 {setup|start|stop|status|restart}"
            echo "  setup   - 下载、配置并启动MediaMTX"
            echo "  start   - 启动MediaMTX"
            echo "  stop    - 停止MediaMTX"
            echo "  status  - 检查MediaMTX状态"
            echo "  restart - 重启MediaMTX"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
