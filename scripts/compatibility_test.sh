#!/bin/bash

# 推流功能兼容性测试脚本
# 测试不同视频格式、分辨率和播放器的兼容性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查MediaMTX状态
check_mediamtx() {
    log_info "检查MediaMTX服务器状态..."
    
    if ! ./scripts/setup_rtsp_server.sh status &>/dev/null; then
        log_warning "MediaMTX未运行，尝试启动..."
        if ! ./scripts/setup_rtsp_server.sh start; then
            log_error "无法启动MediaMTX服务器"
            return 1
        fi
    fi
    
    log_success "MediaMTX服务器运行正常"
    return 0
}

# 测试不同分辨率兼容性
test_resolution_compatibility() {
    log_info "测试分辨率兼容性..."
    
    local test_dir="compatibility_test_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$test_dir"
    
    # 测试分辨率列表
    local resolutions=(
        "320x240"   # QVGA
        "640x480"   # VGA
        "800x600"   # SVGA
        "1024x768"  # XGA
        "1280x720"  # HD 720p
        "1920x1080" # Full HD 1080p
    )
    
    local results_file="$test_dir/resolution_compatibility.txt"
    echo "分辨率兼容性测试结果" > "$results_file"
    echo "=====================" >> "$results_file"
    echo "" >> "$results_file"
    
    for res in "${resolutions[@]}"; do
        log_info "测试分辨率: $res"
        
        local width=$(echo $res | cut -d'x' -f1)
        local height=$(echo $res | cut -d'x' -f2)
        
        # 使用FFmpeg生成测试视频
        local test_video="$test_dir/test_${res}.mp4"
        
        if timeout 30s ffmpeg -f lavfi -i testsrc=duration=5:size=${res}:rate=15 \
            -c:v libx264 -preset fast -b:v 1000k -y "$test_video" &>/dev/null; then
            
            # 验证视频文件
            if ffprobe -v quiet -select_streams v:0 -show_entries stream=width,height \
                -of csv=p=0 "$test_video" | grep -q "${width},${height}"; then
                log_success "分辨率 $res: 兼容"
                echo "$res: 兼容 ✓" >> "$results_file"
            else
                log_warning "分辨率 $res: 编码成功但验证失败"
                echo "$res: 编码成功但验证失败 ⚠" >> "$results_file"
            fi
        else
            log_error "分辨率 $res: 不兼容"
            echo "$res: 不兼容 ✗" >> "$results_file"
        fi
        
        # 清理测试文件
        rm -f "$test_video"
    done
    
    log_success "分辨率兼容性测试完成，结果保存在: $results_file"
}

# 测试不同帧率兼容性
test_framerate_compatibility() {
    log_info "测试帧率兼容性..."
    
    local test_dir="compatibility_test_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$test_dir"
    
    # 测试帧率列表
    local framerates=(5 10 15 24 25 30 50 60)
    
    local results_file="$test_dir/framerate_compatibility.txt"
    echo "帧率兼容性测试结果" > "$results_file"
    echo "==================" >> "$results_file"
    echo "" >> "$results_file"
    
    for fps in "${framerates[@]}"; do
        log_info "测试帧率: ${fps}fps"
        
        local test_video="$test_dir/test_${fps}fps.mp4"
        
        if timeout 30s ffmpeg -f lavfi -i testsrc=duration=3:size=640x480:rate=${fps} \
            -c:v libx264 -preset fast -b:v 1000k -y "$test_video" &>/dev/null; then
            
            # 验证帧率
            local actual_fps=$(ffprobe -v quiet -select_streams v:0 -show_entries stream=r_frame_rate \
                -of csv=p=0 "$test_video" | head -1)
            
            if [ -n "$actual_fps" ]; then
                log_success "帧率 ${fps}fps: 兼容 (实际: $actual_fps)"
                echo "${fps}fps: 兼容 ✓ (实际: $actual_fps)" >> "$results_file"
            else
                log_warning "帧率 ${fps}fps: 编码成功但无法验证"
                echo "${fps}fps: 编码成功但无法验证 ⚠" >> "$results_file"
            fi
        else
            log_error "帧率 ${fps}fps: 不兼容"
            echo "${fps}fps: 不兼容 ✗" >> "$results_file"
        fi
        
        # 清理测试文件
        rm -f "$test_video"
    done
    
    log_success "帧率兼容性测试完成，结果保存在: $results_file"
}

# 测试不同码率兼容性
test_bitrate_compatibility() {
    log_info "测试码率兼容性..."
    
    local test_dir="compatibility_test_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$test_dir"
    
    # 测试码率列表 (bps)
    local bitrates=(100000 500000 1000000 2000000 5000000 10000000)
    
    local results_file="$test_dir/bitrate_compatibility.txt"
    echo "码率兼容性测试结果" > "$results_file"
    echo "==================" >> "$results_file"
    echo "" >> "$results_file"
    
    for bitrate in "${bitrates[@]}"; do
        local bitrate_mb=$(echo "scale=1; $bitrate / 1000000" | bc)
        log_info "测试码率: ${bitrate_mb}Mbps"
        
        local test_video="$test_dir/test_${bitrate_mb}mbps.mp4"
        
        if timeout 30s ffmpeg -f lavfi -i testsrc=duration=3:size=640x480:rate=15 \
            -c:v libx264 -preset fast -b:v ${bitrate} -y "$test_video" &>/dev/null; then
            
            # 验证文件大小合理性
            local file_size=$(stat -c%s "$test_video" 2>/dev/null || echo "0")
            if [ "$file_size" -gt 1000 ]; then
                log_success "码率 ${bitrate_mb}Mbps: 兼容"
                echo "${bitrate_mb}Mbps: 兼容 ✓" >> "$results_file"
            else
                log_warning "码率 ${bitrate_mb}Mbps: 文件过小"
                echo "${bitrate_mb}Mbps: 文件过小 ⚠" >> "$results_file"
            fi
        else
            log_error "码率 ${bitrate_mb}Mbps: 不兼容"
            echo "${bitrate_mb}Mbps: 不兼容 ✗" >> "$results_file"
        fi
        
        # 清理测试文件
        rm -f "$test_video"
    done
    
    log_success "码率兼容性测试完成，结果保存在: $results_file"
}

# 测试RTSP流兼容性
test_rtsp_stream_compatibility() {
    log_info "测试RTSP流兼容性..."
    
    # 确保MediaMTX运行
    if ! check_mediamtx; then
        log_error "无法启动RTSP服务器，跳过RTSP流测试"
        return 1
    fi
    
    local test_dir="compatibility_test_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$test_dir"
    
    local results_file="$test_dir/rtsp_compatibility.txt"
    echo "RTSP流兼容性测试结果" > "$results_file"
    echo "=====================" >> "$results_file"
    echo "" >> "$results_file"
    
    # 测试推流到RTSP服务器
    log_info "测试推流到RTSP服务器..."
    
    # 生成测试视频并推流
    local test_stream="rtmp://localhost:1935/live"
    local view_stream="rtsp://localhost:8554/live"
    
    # 启动推流（后台）
    timeout 10s ffmpeg -f lavfi -i testsrc=duration=30:size=640x480:rate=15 \
        -c:v libx264 -preset fast -b:v 1000k -f flv "$test_stream" &>/dev/null &
    local stream_pid=$!
    
    # 等待推流建立
    sleep 3
    
    # 测试RTSP流是否可访问
    if timeout 5s ffprobe -v quiet "$view_stream" &>/dev/null; then
        log_success "RTSP流: 可访问"
        echo "RTSP流: 可访问 ✓" >> "$results_file"
        
        # 获取流信息
        local stream_info=$(ffprobe -v quiet -show_streams "$view_stream" 2>/dev/null | grep -E "(codec_name|width|height|r_frame_rate)" || echo "无法获取流信息")
        echo "流信息: $stream_info" >> "$results_file"
    else
        log_warning "RTSP流: 无法访问"
        echo "RTSP流: 无法访问 ⚠" >> "$results_file"
    fi
    
    # 停止推流
    kill $stream_pid 2>/dev/null || true
    
    log_success "RTSP流兼容性测试完成，结果保存在: $results_file"
}

# 测试播放器兼容性
test_player_compatibility() {
    log_info "测试播放器兼容性..."
    
    local test_dir="compatibility_test_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$test_dir"
    
    local results_file="$test_dir/player_compatibility.txt"
    echo "播放器兼容性测试结果" > "$results_file"
    echo "=====================" >> "$results_file"
    echo "" >> "$results_file"
    
    # 创建测试视频
    local test_video="$test_dir/test_video.mp4"
    ffmpeg -f lavfi -i testsrc=duration=5:size=640x480:rate=15 \
        -c:v libx264 -preset fast -b:v 1000k -y "$test_video" &>/dev/null
    
    # 测试FFplay
    if command -v ffplay &> /dev/null; then
        log_info "测试FFplay兼容性..."
        if timeout 3s ffplay -autoexit -v quiet "$test_video" &>/dev/null; then
            log_success "FFplay: 兼容"
            echo "FFplay: 兼容 ✓" >> "$results_file"
        else
            log_warning "FFplay: 可能不兼容"
            echo "FFplay: 可能不兼容 ⚠" >> "$results_file"
        fi
    else
        log_info "FFplay未安装"
        echo "FFplay: 未安装 -" >> "$results_file"
    fi
    
    # 测试VLC（如果可用）
    if command -v vlc &> /dev/null; then
        log_info "测试VLC兼容性..."
        # VLC测试比较复杂，这里只检查是否能识别文件
        if vlc --intf dummy --play-and-exit --quiet "$test_video" &>/dev/null; then
            log_success "VLC: 兼容"
            echo "VLC: 兼容 ✓" >> "$results_file"
        else
            log_warning "VLC: 可能不兼容"
            echo "VLC: 可能不兼容 ⚠" >> "$results_file"
        fi
    else
        log_info "VLC未安装"
        echo "VLC: 未安装 -" >> "$results_file"
    fi
    
    # 清理测试文件
    rm -f "$test_video"
    
    log_success "播放器兼容性测试完成，结果保存在: $results_file"
}

# 生成兼容性报告
generate_compatibility_report() {
    log_info "生成兼容性测试报告..."
    
    local report_file="compatibility_report_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
# 推流功能兼容性测试报告

## 测试时间
$(date)

## 系统信息
操作系统: $(lsb_release -d 2>/dev/null | cut -f2 || uname -s)
FFmpeg版本: $(ffmpeg -version | head -n1 | cut -d' ' -f3)

## 测试摘要
本次测试验证了推流功能在不同配置下的兼容性，包括：
- 分辨率兼容性
- 帧率兼容性  
- 码率兼容性
- RTSP流兼容性
- 播放器兼容性

## 测试结果
详细结果请查看各个测试生成的结果文件。

## 建议
1. 推荐使用标准分辨率（如640x480, 1280x720, 1920x1080）
2. 帧率建议设置为15-30fps以获得最佳兼容性
3. 码率应根据分辨率和网络条件合理设置
4. 使用FFplay或VLC等标准播放器进行测试

EOF
    
    log_success "兼容性测试报告生成完成: $report_file"
}

# 主函数
main() {
    case "${1:-all}" in
        resolution)
            test_resolution_compatibility
            ;;
        framerate)
            test_framerate_compatibility
            ;;
        bitrate)
            test_bitrate_compatibility
            ;;
        rtsp)
            test_rtsp_stream_compatibility
            ;;
        player)
            test_player_compatibility
            ;;
        all)
            log_info "=== 推流功能兼容性测试 ==="
            test_resolution_compatibility
            test_framerate_compatibility
            test_bitrate_compatibility
            test_rtsp_stream_compatibility
            test_player_compatibility
            generate_compatibility_report
            log_success "=== 所有兼容性测试完成 ==="
            ;;
        *)
            echo "用法: $0 {resolution|framerate|bitrate|rtsp|player|all}"
            echo "  resolution - 分辨率兼容性测试"
            echo "  framerate  - 帧率兼容性测试"
            echo "  bitrate    - 码率兼容性测试"
            echo "  rtsp       - RTSP流兼容性测试"
            echo "  player     - 播放器兼容性测试"
            echo "  all        - 运行所有测试"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
