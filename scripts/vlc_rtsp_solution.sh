#!/bin/bash

# VLC RTSP播放解决方案脚本
# 提供完整的VLC播放器RTSP流播放解决方案

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示VLC播放解决方案
show_vlc_solution() {
    echo
    log_info "=== MediaMTX RTSP服务器 VLC播放器解决方案 ==="
    echo
    
    log_info "问题分析："
    echo "  - VLC 3.0.16版本对RTSP协议要求较严格"
    echo "  - 需要正确的命令行参数和配置"
    echo "  - MediaMTX服务器需要完整的协议支持配置"
    echo
    
    log_success "解决方案："
    echo
    echo "1. 确保MediaMTX服务器正在运行："
    echo "   ./scripts/setup_rtsp_server.sh status"
    echo
    echo "2. 启动推流（必须先有推流）："
    echo "   ffmpeg -re -i \"视频文件.mp4\" -c:v libx264 -preset fast -b:v 1000k -s 640x480 -f rtsp rtsp://localhost:8554/live"
    echo
    echo "3. VLC播放命令（推荐方法）："
    echo "   vlc --intf dummy --play-and-exit rtsp://localhost:8554/live"
    echo
    echo "4. VLC图形界面播放："
    echo "   - 打开VLC播放器"
    echo "   - 媒体 → 打开网络串流"
    echo "   - 输入URL: rtsp://localhost:8554/live"
    echo "   - 点击播放"
    echo
    echo "5. 高级VLC播放选项："
    echo "   # TCP传输（更稳定）"
    echo "   vlc --rtsp-tcp rtsp://localhost:8554/live"
    echo
    echo "   # 低延迟播放"
    echo "   vlc --rtsp-tcp --network-caching=300 rtsp://localhost:8554/live"
    echo
    echo "   # 调试模式"
    echo "   vlc --intf dummy --extraintf logger --verbose 2 rtsp://localhost:8554/live"
    echo
}

# 检查系统状态
check_system_status() {
    log_info "检查系统状态..."
    
    # 检查MediaMTX
    if ! netstat -tuln | grep -q ":8554"; then
        log_error "MediaMTX RTSP服务器未运行"
        log_info "请先启动: ./scripts/setup_rtsp_server.sh start"
        return 1
    fi
    log_success "MediaMTX RTSP服务器正在运行"
    
    # 检查VLC
    if ! command -v vlc &> /dev/null; then
        log_error "VLC未安装"
        log_info "请安装VLC: sudo apt install vlc"
        return 1
    fi
    
    VLC_VERSION=$(vlc --version 2>&1 | head -n1 | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' || echo "未知")
    log_success "VLC已安装，版本: $VLC_VERSION"
    
    return 0
}

# 启动演示推流
start_demo_stream() {
    log_info "启动演示推流..."
    
    # 停止可能存在的推流
    pkill -f "ffmpeg.*rtsp://localhost:8554" || true
    sleep 2
    
    # 启动演示推流
    ffmpeg -f lavfi -i testsrc=duration=300:size=640x480:rate=15 \
           -f lavfi -i sine=frequency=1000:duration=300 \
           -c:v libx264 -preset fast -b:v 1000k \
           -c:a aac -b:a 128k \
           -f rtsp rtsp://localhost:8554/live > /dev/null 2>&1 &
    
    DEMO_PID=$!
    echo $DEMO_PID > demo_stream.pid
    
    log_success "演示推流已启动 (PID: $DEMO_PID)"
    
    # 等待推流建立
    sleep 5
    
    # 验证推流状态
    if tail -n 10 mediamtx.log | grep -q "is publishing to path 'live'"; then
        log_success "演示推流已成功建立"
        return 0
    else
        log_error "演示推流建立失败"
        return 1
    fi
}

# 停止演示推流
stop_demo_stream() {
    if [ -f demo_stream.pid ]; then
        DEMO_PID=$(cat demo_stream.pid)
        if kill -0 $DEMO_PID 2>/dev/null; then
            log_info "停止演示推流..."
            kill $DEMO_PID 2>/dev/null || true
            wait $DEMO_PID 2>/dev/null || true
            log_success "演示推流已停止"
        fi
        rm -f demo_stream.pid
    fi
}

# 测试VLC播放
test_vlc_playback() {
    log_info "测试VLC播放..."
    
    local test_commands=(
        "vlc --intf dummy --play-and-exit rtsp://localhost:8554/live"
        "vlc --intf dummy --play-and-exit --rtsp-tcp rtsp://localhost:8554/live"
        "vlc --intf dummy --play-and-exit --rtsp-tcp --network-caching=1000 rtsp://localhost:8554/live"
    )
    
    for cmd in "${test_commands[@]}"; do
        log_info "测试命令: $cmd"
        
        if timeout 10s $cmd > /dev/null 2>&1; then
            log_success "VLC播放测试成功"
            return 0
        else
            log_warning "此命令测试失败，尝试下一个..."
        fi
        
        sleep 2
    done
    
    log_error "所有VLC播放测试都失败"
    return 1
}

# 运行完整演示
run_demo() {
    log_info "=== 运行VLC播放完整演示 ==="
    
    # 检查系统状态
    if ! check_system_status; then
        exit 1
    fi
    
    # 启动演示推流
    if ! start_demo_stream; then
        log_error "无法启动演示推流"
        exit 1
    fi
    
    # 显示解决方案
    show_vlc_solution
    
    # 测试VLC播放
    if test_vlc_playback; then
        log_success "VLC播放演示成功！"
        echo
        log_info "现在您可以使用以下命令播放RTSP流："
        echo "  vlc rtsp://localhost:8554/live"
        echo "  或者"
        echo "  vlc --rtsp-tcp rtsp://localhost:8554/live"
        echo
        log_info "演示推流将继续运行5分钟，您可以测试VLC播放"
        log_info "要停止演示推流，请运行: $0 stop"
    else
        log_error "VLC播放演示失败"
        stop_demo_stream
        exit 1
    fi
}

# 清理函数
cleanup() {
    stop_demo_stream
}

# 设置清理陷阱
trap cleanup EXIT

# 主函数
main() {
    case "${1:-demo}" in
        demo)
            run_demo
            ;;
        solution)
            show_vlc_solution
            ;;
        test)
            check_system_status
            test_vlc_playback
            ;;
        start)
            check_system_status
            start_demo_stream
            ;;
        stop)
            stop_demo_stream
            ;;
        *)
            echo "用法: $0 [demo|solution|test|start|stop]"
            echo "  demo     - 运行完整演示（默认）"
            echo "  solution - 显示VLC播放解决方案"
            echo "  test     - 测试VLC播放"
            echo "  start    - 启动演示推流"
            echo "  stop     - 停止演示推流"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
