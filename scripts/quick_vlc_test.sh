#!/bin/bash

# 快速VLC兼容性测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查MediaMTX状态
if ! netstat -tuln | grep -q ":8554"; then
    log_error "MediaMTX RTSP服务器未运行"
    log_info "请先启动: ./scripts/setup_rtsp_server.sh start"
    exit 1
fi

log_info "=== 快速VLC兼容性测试 ==="

# 启动测试推流
log_info "启动测试推流..."
ffmpeg -f lavfi -i testsrc=duration=30:size=640x480:rate=15 -f lavfi -i sine=frequency=1000:duration=30 -c:v libx264 -preset fast -b:v 1000k -c:a aac -b:a 128k -f rtsp rtsp://localhost:8554/test > /dev/null 2>&1 &
FFMPEG_PID=$!

# 等待推流启动
sleep 3

log_success "测试推流已启动 (PID: $FFMPEG_PID)"

# 测试ffplay
log_info "测试ffplay播放..."
timeout 3s ffplay -rtsp_transport tcp -autoexit rtsp://localhost:8554/test > /dev/null 2>&1
if [ $? -eq 0 ] || [ $? -eq 124 ]; then
    log_success "ffplay播放成功"
else
    log_error "ffplay播放失败"
fi

# 测试VLC（如果可用）
if command -v vlc &> /dev/null; then
    log_info "测试VLC播放..."
    timeout 3s vlc --intf dummy --play-and-exit rtsp://localhost:8554/test > /dev/null 2>&1
    if [ $? -eq 0 ] || [ $? -eq 124 ]; then
        log_success "VLC播放成功"
    else
        log_error "VLC播放失败"
    fi
    
    # 测试斜杠结尾URL
    log_info "测试VLC播放（斜杠结尾URL）..."
    timeout 3s vlc --intf dummy --play-and-exit rtsp://localhost:8554/test/ > /dev/null 2>&1
    if [ $? -eq 0 ] || [ $? -eq 124 ]; then
        log_success "VLC播放成功（斜杠结尾URL）"
    else
        log_error "VLC播放失败（斜杠结尾URL）"
    fi
else
    log_info "VLC未安装，跳过VLC测试"
fi

# 清理
log_info "清理测试环境..."
kill $FFMPEG_PID 2>/dev/null || true
wait $FFMPEG_PID 2>/dev/null || true

log_success "测试完成"

# 显示使用指南
echo
log_info "=== VLC播放器使用指南 ==="
echo "1. 确保有推流正在进行："
echo "   ffmpeg -re -i 'video.mp4' -c:v libx264 -preset fast -b:v 1000k -s 640x480 -f rtsp rtsp://localhost:8554/live"
echo
echo "2. 使用VLC播放："
echo "   vlc rtsp://localhost:8554/live"
echo "   或者: vlc rtsp://localhost:8554/live/"
echo
echo "3. 使用TCP传输（推荐）："
echo "   vlc --rtsp-tcp rtsp://localhost:8554/live"
echo
echo "4. 使用ffplay验证："
echo "   ffplay -rtsp_transport tcp rtsp://localhost:8554/live"
