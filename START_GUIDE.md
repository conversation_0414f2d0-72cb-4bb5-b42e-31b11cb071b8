# 拉吊索缺损识别系统一键启动指南

## 概述

`start.sh` 是拉吊索缺损识别系统的一键启动脚本，解决了主程序对MediaMTX服务器的依赖问题。该脚本会自动启动MediaMTX RTSP服务器，等待服务完全就绪后，再启动故障检测主程序，确保推流连接不会失败。

## 快速开始

### 基本使用

```bash
# 一键启动完整系统（推荐）
./start.sh

# 后台运行模式
./start.sh start-bg

# 检查系统状态
./start.sh status

# 停止所有服务
./start.sh stop
```

### 首次使用

1. **确保项目已编译**：
   ```bash
   ./build.sh
   ```

2. **安装必要依赖**（如果提示缺失）：
   ```bash
   sudo apt update
   sudo apt install -y netstat-persistent netcat-openbsd jq
   ```

3. **启动系统**：
   ```bash
   ./start.sh
   ```

## 启动模式详解

### 1. 完整启动模式

```bash
./start.sh              # 前台运行（默认）
./start.sh start         # 前台运行（显式指定）
./start.sh start-bg      # 后台运行
./start.sh background    # 后台运行（别名）
```

**特点**：
- 自动启动MediaMTX服务器
- 等待服务完全就绪
- 启动故障检测主程序
- 前台模式可直接看到程序输出
- 后台模式将输出重定向到日志文件

### 2. 分步启动模式

```bash
# 仅启动MediaMTX服务器
./start.sh server-only

# 仅启动主程序（需要服务器已运行）
./start.sh app-only
```

**使用场景**：
- 调试MediaMTX服务器配置
- 独立测试推流功能
- 分步验证系统组件

### 3. 管理模式

```bash
# 检查系统状态
./start.sh status

# 停止所有服务
./start.sh stop

# 重启系统
./start.sh restart

# 显示帮助信息
./start.sh --help
```

## 系统状态说明

### 正常运行状态

执行 `./start.sh status` 时应该看到：

```
=== MediaMTX服务器状态 ===
[SUCCESS] MediaMTX正在运行 (PID: xxxxx)
[SUCCESS] RTSP端口8554正常监听
[SUCCESS] RTMP端口1935正常监听

=== 主程序状态 ===
[SUCCESS] 主程序正在运行 (PID: xxxxx)

=== 端口监听状态 ===
[SUCCESS] RTSP端口 8554 正在监听

=== 配置信息 ===
[INFO] RTSP服务器: *************:8554
[INFO] 推流地址: rtsp://*************:8554/live
```

### 异常状态处理

如果看到警告或错误信息：

1. **MediaMTX未运行**：
   ```bash
   ./start.sh server-only  # 单独启动服务器
   ```

2. **主程序未运行**：
   ```bash
   ./start.sh app-only     # 单独启动主程序
   ```

3. **端口被占用**：
   ```bash
   ./start.sh stop         # 停止所有服务
   ./start.sh start        # 重新启动
   ```

## 配置说明

### RTSP服务器配置

脚本会自动从 `config/system_config.json` 读取RTSP服务器配置：

```json
{
  "streaming": {
    "rtsp_server_address": "*************",
    "rtsp_server_port": 8554
  }
}
```

### 智能地址处理

如果配置的地址无法连接，脚本会自动尝试：
1. 配置的地址（如 *************:8554）
2. localhost:8554
3. 127.0.0.1:8554

这确保了在不同网络环境下的兼容性。

## 日志和监控

### 日志文件位置

- **后台模式日志**：`fault_detect.log`
- **MediaMTX日志**：`mediamtx.log`
- **脚本运行日志**：实时输出到终端

### 实时监控

```bash
# 查看主程序日志
tail -f fault_detect.log

# 查看MediaMTX日志
tail -f mediamtx.log

# 检查系统状态
watch -n 5 './start.sh status'
```

## 故障排除

### 常见问题

1. **依赖缺失**：
   ```
   [ERROR] 缺少以下依赖：
     - 系统命令: jq
   ```
   **解决**：按提示安装缺失的依赖

2. **主程序不存在**：
   ```
   [ERROR] 主程序不存在: /path/to/FaultDetectRefactored
   ```
   **解决**：运行 `./build.sh` 编译项目

3. **端口连接失败**：
   ```
   [ERROR] 无法连接到MediaMTX服务器
   ```
   **解决**：检查网络配置或使用 `./start.sh restart`

### 调试模式

如需详细的调试信息，可以修改脚本开头添加：
```bash
set -x  # 启用调试模式
```

## 最佳实践

### 生产环境使用

1. **使用后台模式**：
   ```bash
   ./start.sh start-bg
   ```

2. **定期检查状态**：
   ```bash
   # 添加到crontab
   */5 * * * * cd /path/to/project && ./start.sh status >> status.log
   ```

3. **自动重启**（可选）：
   ```bash
   # 检查并自动重启异常服务
   ./start.sh status || ./start.sh restart
   ```

### 开发环境使用

1. **使用前台模式**便于调试：
   ```bash
   ./start.sh
   ```

2. **分步启动**便于测试：
   ```bash
   ./start.sh server-only  # 先启动服务器
   # 进行配置调试
   ./start.sh app-only     # 再启动主程序
   ```

## 技术支持

如遇到问题，请：

1. 查看 `./start.sh --help` 获取使用帮助
2. 检查 `./start.sh status` 了解系统状态
3. 查看相关日志文件获取详细错误信息
4. 参考 `readme.txt` 了解系统架构和更新记录

---

**注意**：该脚本设计遵循高内聚低耦合原则，具有良好的错误处理和用户体验。如需自定义功能，可以参考脚本源码进行扩展。
