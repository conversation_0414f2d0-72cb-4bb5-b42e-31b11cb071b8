# 拉吊索缺损识别系统

基于C++和传统图像处理算法的多路摄像头缺损识别系统，用于自动检测拉吊索表面的各类损伤。

## 项目概述

本系统采用4路摄像头同时采集图像，使用传统图像处理算法实现实时缺损检测，支持7种类型的损伤识别：
- 裂缝检测 (≥0.1mm)
- 磨损检测
- 刮伤检测  
- 凹坑检测
- 鼓包检测
- 老化检测
- 安装破损检测

## 技术特性

- **多摄像头同步**: 支持4路摄像头同时采集 (640x480@15FPS)
- **实时处理**: 传统图像处理算法，处理延迟<500ms
- **高精度检测**: 裂缝检测精度≥0.1mm，块状损伤≥10mm×10mm
- **模块化设计**: 采用模块化架构，便于扩展和维护
- **跨平台支持**: 支持Windows和Linux系统
- **视频流推送**: 实时H.264编码和RTSP协议推流 🆕
- **数据存储**: SQLite数据库存储检测结果

## 系统要求

### 硬件要求
- CPU: 多核处理器 (推荐4核以上)
- 内存: 4GB以上
- 存储: 100GB以上可用空间
- USB接口: 4个USB 3.0接口
- 摄像头: 4个UVC兼容摄像头

### 软件要求
- 操作系统: Windows 10/11 或 Ubuntu 18.04+
- 编译器: GCC 7.0+ 或 MSVC 2019+
- CMake: 3.10+
- OpenCV: 4.0+
- FFmpeg: 4.0+ (推流功能必需)
- MediaMTX: 最新版本 (推荐的RTSP服务器)

## 编译安装

### 1. 安装依赖

#### Ubuntu/Linux
```bash
# 安装基础开发工具
sudo apt update
sudo apt install build-essential cmake git

# 安装OpenCV
sudo apt install libopencv-dev

# 安装FFmpeg (可选)
sudo apt install libavcodec-dev libavformat-dev libavutil-dev libswscale-dev

# 安装V4L2开发库
sudo apt install libv4l-dev
```

#### Windows
1. 安装Visual Studio 2019或更新版本
2. 下载并安装OpenCV预编译库
3. 下载并安装CMake
4. (可选) 安装FFmpeg开发库

### 2. 编译项目

```bash
# 克隆项目
git clone <repository-url>
cd fault_detect

# 创建构建目录
mkdir build
cd build

# 配置项目
cmake ..

# 编译
cmake --build . --config Release

# 或者使用make (Linux)
make -j4
```

### 3. 运行程序

```bash
# 在build目录下运行
./FaultDetect

# 或者在Windows下
FaultDetect.exe
```

## 配置说明

系统配置文件位于 `config/system_config.json`，主要配置项包括：

### 摄像头配置
```json
"camera": {
  "count": 4,           // 摄像头数量
  "width": 640,         // 图像宽度
  "height": 480,        // 图像高度
  "target_fps": 15      // 目标帧率
}
```

### 检测参数
```json
"detection": {
  "min_crack_width_mm": 0.1,    // 最小裂缝宽度
  "min_damage_size_mm": 10.0,   // 最小损伤尺寸
  "confidence_threshold": 0.6    // 置信度阈值
}
```

### 算法参数
各类检测算法都有独立的参数配置，可根据实际需求调整。

## 使用方法

### 1. 系统启动
```bash
./FaultDetect
```

### 2. 查看系统状态
程序启动后会显示各摄像头的连接状态和运行参数。

### 3. 检测结果
- 检测结果会实时显示在控制台
- 原始图像和处理结果保存在 `output/` 目录
- 检测数据以JSON格式保存

### 4. 停止系统
按 `Ctrl+C` 安全停止系统。

## 输出文件

### 目录结构
```
output/
├── images/           # 原始图像
├── processed/        # 处理后图像
├── results/          # 检测结果JSON文件
└── logs/            # 系统日志
```

### 检测结果格式
```json
{
  "timestamp": "2025-07-29 17:30:00.123",
  "camera_id": 0,
  "processing_time_ms": 45.6,
  "damages": [
    {
      "type": "CRACK",
      "confidence": 0.85,
      "bounding_box": [100, 150, 50, 200],
      "area_pixels": 1250,
      "length_mm": 25.5,
      "width_mm": 0.8,
      "center": [125, 250],
      "description": "检测到裂缝"
    }
  ]
}
```

## 视频流推送功能 🆕

### 功能特性
- **实时H.264编码**: 使用FFmpeg进行高效视频编码
- **RTSP协议支持**: 标准协议，兼容VLC等播放器
- **自适应质量控制**: 根据网络状况自动调整码率和帧率
- **多客户端支持**: 支持多个客户端同时观看
- **低延迟设计**: 推流延迟<500ms

### 快速使用

1. **安装RTSP服务器**
```bash
# 下载MediaMTX
wget https://github.com/bluenviron/mediamtx/releases/latest/download/mediamtx_linux_amd64.tar.gz
tar -xzf mediamtx_linux_amd64.tar.gz
chmod +x mediamtx

# 启动服务器
./mediamtx
```

2. **启用推流功能**
编辑 `config/system_config.json`:
```json
{
  "streaming": {
    "enabled": true
  }
}
```

3. **启动系统**
```bash
cd build
./bin/FaultDetectRefactored
```

4. **观看视频流**
使用VLC播放器打开: `rtsp://localhost:8554/live`

### 测试验证
```bash
# 运行集成测试
chmod +x scripts/test_streaming.sh
./scripts/test_streaming.sh

# 运行单元测试
./bin/FaultDetectRefactored --test streaming
```

详细使用指南请参考: [STREAMING_GUIDE.md](STREAMING_GUIDE.md)

## 性能监控

系统提供实时性能监控：
- 帧率统计
- 处理时间统计
- 检测准确率统计
- 推流状态和码率统计 🆕
- 系统资源使用情况

## 故障排除

### 常见问题

1. **摄像头无法连接**
   - 检查USB连接
   - 确认摄像头驱动正常
   - 检查设备权限

2. **帧率过低**
   - 检查USB带宽
   - 降低图像分辨率
   - 优化算法参数

3. **检测精度不足**
   - 调整算法参数
   - 改善光照条件
   - 重新标定摄像头

4. **内存使用过高**
   - 减少队列大小
   - 降低图像分辨率
   - 优化内存管理

### 日志分析
系统日志保存在 `logs/` 目录，包含详细的运行信息和错误记录。

## 开发说明

### 项目结构
```
fault_detect/
├── src/                    # 源代码
│   ├── camera/            # 摄像头管理
│   ├── image_process/     # 图像处理
│   ├── algorithm/         # 检测算法
│   ├── rtsp/             # RTSP推流
│   └── storage/          # 数据存储
├── include/               # 头文件
├── test/                 # 测试代码
├── config/               # 配置文件
└── docs/                 # 文档
```

### 扩展开发
- 添加新的检测算法
- 支持更多摄像头
- 集成深度学习模型
- 添加Web界面

## 版本历史

- **v1.0.0** (2025-07-29)
  - 初始版本发布
  - 支持4路摄像头采集
  - 实现7种缺损检测算法
  - 基础RTSP推流功能

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请联系开发团队。
