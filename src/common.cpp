#include "../include/common.h"
#include <iostream>
#include <iomanip>
#include <sstream>
#include <filesystem>
#include <cmath>

namespace Utils {

std::string getCurrentTimeString() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;
    
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count();
    return ss.str();
}

std::string damageTypeToString(DamageType type) {
    switch (type) {
        case DamageType::NONE: return "无损伤";
        case DamageType::CRACK: return "裂缝";
        case DamageType::WEAR: return "磨损";
        case DamageType::SCRATCH: return "刮伤";
        case DamageType::PIT: return "凹坑";
        case DamageType::BULGE: return "鼓包";
        case DamageType::AGING: return "老化";
        case DamageType::INSTALL_DAMAGE: return "安装破损";
        default: return "未知";
    }
}

#ifdef USE_OPENCV
double calculateDistance(const cv::Point2f& p1, const cv::Point2f& p2) {
    double dx = p1.x - p2.x;
    double dy = p1.y - p2.y;
    return std::sqrt(dx * dx + dy * dy);
}
#else
double calculateDistance(const SimplePoint& p1, const SimplePoint& p2) {
    double dx = p1.x - p2.x;
    double dy = p1.y - p2.y;
    return std::sqrt(dx * dx + dy * dy);
}
#endif

double pixelToMm(double pixels, double pixelSize) {
    return pixels * pixelSize;
}

double mmToPixel(double mm, double pixelSize) {
    return mm / pixelSize;
}

bool createDirectory(const std::string& path) {
    try {
        std::filesystem::create_directories(path);
        return true;
    } catch (const std::exception& e) {
        logError("创建目录失败: " + path + ", 错误: " + e.what());
        return false;
    }
}

#ifdef USE_OPENCV
bool saveImage(const cv::Mat& image, const std::string& filename) {
    if (image.empty()) {
        logError("尝试保存空图像: " + filename);
        return false;
    }

    try {
        // 确保目录存在
        std::filesystem::path filePath(filename);
        if (filePath.has_parent_path()) {
            createDirectory(filePath.parent_path().string());
        }

        bool success = cv::imwrite(filename, image);
        if (!success) {
            logError("保存图像失败: " + filename);
        }
        return success;
    } catch (const std::exception& e) {
        logError("保存图像异常: " + filename + ", 错误: " + e.what());
        return false;
    }
}
#else
bool saveImage(const SimpleImage& image, const std::string& filename) {
    if (image.empty()) {
        logError("尝试保存空图像: " + filename);
        return false;
    }

    // 简化版本：暂时只记录日志
    logInfo("保存图像 (简化版本): " + filename);
    return true;
}
#endif

void logInfo(const std::string& message) {
    std::cout << "[INFO] " << getCurrentTimeString() << " - " << message << std::endl;
}

void logWarning(const std::string& message) {
    std::cout << "[WARN] " << getCurrentTimeString() << " - " << message << std::endl;
}

void logError(const std::string& message) {
    std::cerr << "[ERROR] " << getCurrentTimeString() << " - " << message << std::endl;
}

} // namespace Utils
