#include "../include/common_simple.h"
#include <iostream>

int main() {
    Utils::logInfo("=== 简单测试程序 ===");
    
    // 测试基础功能
    Utils::logInfo("当前时间: " + Utils::getCurrentTimeString());
    
    // 测试损伤类型
    for (int i = 0; i <= 7; ++i) {
        DamageType type = static_cast<DamageType>(i);
        Utils::logInfo("损伤类型 " + std::to_string(i) + ": " + Utils::damageTypeToString(type));
    }
    
    // 测试单位转换
    double pixels = 100.0;
    double mm = Utils::pixelToMm(pixels);
    Utils::logInfo("100像素 = " + std::to_string(mm) + "毫米");
    
    // 测试距离计算
    SimplePoint p1(0, 0);
    SimplePoint p2(3, 4);
    double distance = Utils::calculateDistance(p1, p2);
    Utils::logInfo("距离: " + std::to_string(distance));
    
    // 测试目录创建
    if (Utils::createDirectory("output/test")) {
        Utils::logInfo("目录创建成功");
    }
    
    // 测试数据结构
    DamageInfo damage;
    damage.type = DamageType::CRACK;
    damage.area = 100.0;
    damage.confidence = 0.8;
    Utils::logInfo("损伤类型: " + Utils::damageTypeToString(damage.type));
    Utils::logInfo("面积: " + std::to_string(damage.area));
    Utils::logInfo("置信度: " + std::to_string(damage.confidence));
    
    Utils::logInfo("=== 测试完成 ===");
    return 0;
}
