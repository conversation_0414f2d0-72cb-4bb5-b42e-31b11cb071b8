#include "../include/database_manager.h"
#include "../include/damage_detection_engine.h"
#include "../include/common.h"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <filesystem>

DatabaseManager::DatabaseManager(const std::string& dbPath) 
    : dbPath_(dbPath), db_(nullptr), isInitialized_(false) {
}

DatabaseManager::~DatabaseManager() {
    close();
}

bool DatabaseManager::initialize() {
    std::lock_guard<std::mutex> lock(dbMutex_);
    
    if (isInitialized_) {
        return true;
    }
    
    try {
        // 确保输出目录存在
        std::filesystem::path dbFile(dbPath_);
        std::filesystem::create_directories(dbFile.parent_path());
        
        // 打开数据库连接
        int result = sqlite3_open(dbPath_.c_str(), &db_);
        if (result != SQLITE_OK) {
            logDatabaseError("打开数据库", sqlite3_errmsg(db_));
            return false;
        }
        
        // 启用外键约束
        if (!executeSql("PRAGMA foreign_keys = ON;")) {
            logDatabaseError("启用外键约束", "");
            return false;
        }
        
        // 设置WAL模式以提高并发性能
        if (!executeSql("PRAGMA journal_mode = WAL;")) {
            logDatabaseError("设置WAL模式", "");
            return false;
        }
        
        // 创建表
        if (!createTables()) {
            return false;
        }
        
        isInitialized_ = true;
        Utils::logInfo("数据库初始化成功: " + dbPath_);
        return true;
        
    } catch (const std::exception& e) {
        logDatabaseError("初始化数据库", e.what());
        return false;
    }
}

void DatabaseManager::close() {
    std::lock_guard<std::mutex> lock(dbMutex_);
    
    if (db_) {
        sqlite3_close(db_);
        db_ = nullptr;
    }
    isInitialized_ = false;
}

bool DatabaseManager::createTables() {
    // 创建检测会话表
    std::string createSessionsTable = R"(
        CREATE TABLE IF NOT EXISTS detection_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            camera_id INTEGER NOT NULL,
            timestamp TEXT NOT NULL,
            damage_count INTEGER NOT NULL DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
    )";
    
    if (!executeSql(createSessionsTable)) {
        logDatabaseError("创建detection_sessions表", "");
        return false;
    }
    
    // 创建损伤结果表
    std::string createResultsTable = R"(
        CREATE TABLE IF NOT EXISTS damage_results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id INTEGER NOT NULL,
            damage_type TEXT NOT NULL,
            confidence REAL NOT NULL,
            size_mm REAL NOT NULL,
            center_x REAL NOT NULL,
            center_y REAL NOT NULL,
            bbox_x INTEGER NOT NULL,
            bbox_y INTEGER NOT NULL,
            bbox_width INTEGER NOT NULL,
            bbox_height INTEGER NOT NULL,
            description TEXT,
            timestamp TEXT NOT NULL,
            FOREIGN KEY (session_id) REFERENCES detection_sessions(id) ON DELETE CASCADE
        );
    )";
    
    if (!executeSql(createResultsTable)) {
        logDatabaseError("创建damage_results表", "");
        return false;
    }
    
    // 创建索引以提高查询性能
    std::vector<std::string> indexes = {
        "CREATE INDEX IF NOT EXISTS idx_sessions_camera_time ON detection_sessions(camera_id, timestamp);",
        "CREATE INDEX IF NOT EXISTS idx_sessions_created_at ON detection_sessions(created_at);",
        "CREATE INDEX IF NOT EXISTS idx_results_session_id ON damage_results(session_id);",
        "CREATE INDEX IF NOT EXISTS idx_results_type ON damage_results(damage_type);",
        "CREATE INDEX IF NOT EXISTS idx_results_timestamp ON damage_results(timestamp);"
    };
    
    for (const auto& indexSql : indexes) {
        if (!executeSql(indexSql)) {
            logDatabaseError("创建索引", indexSql);
            return false;
        }
    }
    
    Utils::logInfo("数据库表和索引创建成功");
    return true;
}

bool DatabaseManager::executeSql(const std::string& sql) {
    char* errorMsg = nullptr;
    int result = sqlite3_exec(db_, sql.c_str(), nullptr, nullptr, &errorMsg);
    
    if (result != SQLITE_OK) {
        std::string error = errorMsg ? errorMsg : "未知错误";
        logDatabaseError("执行SQL", error);
        if (errorMsg) {
            sqlite3_free(errorMsg);
        }
        return false;
    }
    
    return true;
}

bool DatabaseManager::saveDetectionResults(const std::vector<DamageResult>& results, int cameraId) {
    if (!isInitialized_) {
        Utils::logError("数据库未初始化");
        return false;
    }
    
    if (results.empty()) {
        return true; // 空结果也算成功
    }
    
    std::lock_guard<std::mutex> lock(dbMutex_);
    
    try {
        // 开始事务
        if (!beginTransaction()) {
            return false;
        }
        
        // 插入检测会话
        std::string timestamp = getCurrentTimeString();
        int sessionId = insertDetectionSession(cameraId, timestamp, static_cast<int>(results.size()));
        
        if (sessionId == -1) {
            rollbackTransaction();
            return false;
        }
        
        // 插入每个损伤结果
        for (const auto& result : results) {
            if (!insertDamageResult(sessionId, result)) {
                rollbackTransaction();
                return false;
            }
        }
        
        // 提交事务
        if (!commitTransaction()) {
            rollbackTransaction();
            return false;
        }
        
        Utils::logInfo("检测结果已保存到数据库，会话ID: " + std::to_string(sessionId) + 
                      "，损伤数量: " + std::to_string(results.size()));
        return true;
        
    } catch (const std::exception& e) {
        rollbackTransaction();
        logDatabaseError("保存检测结果", e.what());
        return false;
    }
}

bool DatabaseManager::beginTransaction() {
    return executeSql("BEGIN TRANSACTION;");
}

bool DatabaseManager::commitTransaction() {
    return executeSql("COMMIT;");
}

bool DatabaseManager::rollbackTransaction() {
    return executeSql("ROLLBACK;");
}

int DatabaseManager::insertDetectionSession(int cameraId, const std::string& timestamp, int damageCount) {
    const char* sql = "INSERT INTO detection_sessions (camera_id, timestamp, damage_count) VALUES (?, ?, ?);";
    
    sqlite3_stmt* stmt;
    int result = sqlite3_prepare_v2(db_, sql, -1, &stmt, nullptr);
    
    if (result != SQLITE_OK) {
        logDatabaseError("准备插入会话语句", sqlite3_errmsg(db_));
        return -1;
    }
    
    // 绑定参数
    sqlite3_bind_int(stmt, 1, cameraId);
    sqlite3_bind_text(stmt, 2, timestamp.c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_int(stmt, 3, damageCount);
    
    result = sqlite3_step(stmt);
    int sessionId = -1;
    
    if (result == SQLITE_DONE) {
        sessionId = static_cast<int>(sqlite3_last_insert_rowid(db_));
    } else {
        logDatabaseError("插入检测会话", sqlite3_errmsg(db_));
    }
    
    sqlite3_finalize(stmt);
    return sessionId;
}

bool DatabaseManager::insertDamageResult(int sessionId, const DamageResult& result) {
    const char* sql = R"(
        INSERT INTO damage_results 
        (session_id, damage_type, confidence, size_mm, center_x, center_y, 
         bbox_x, bbox_y, bbox_width, bbox_height, description, timestamp) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
    )";
    
    sqlite3_stmt* stmt;
    int sqlResult = sqlite3_prepare_v2(db_, sql, -1, &stmt, nullptr);
    
    if (sqlResult != SQLITE_OK) {
        logDatabaseError("准备插入损伤结果语句", sqlite3_errmsg(db_));
        return false;
    }
    
    // 绑定参数
    sqlite3_bind_int(stmt, 1, sessionId);
    std::string typeStr = damageTypeToString(result.type);
    sqlite3_bind_text(stmt, 2, typeStr.c_str(), -1, SQLITE_TRANSIENT);
    sqlite3_bind_double(stmt, 3, result.confidence);
    sqlite3_bind_double(stmt, 4, result.size_mm);
    sqlite3_bind_double(stmt, 5, result.center.x);
    sqlite3_bind_double(stmt, 6, result.center.y);
    sqlite3_bind_int(stmt, 7, result.boundingBox.x);
    sqlite3_bind_int(stmt, 8, result.boundingBox.y);
    sqlite3_bind_int(stmt, 9, result.boundingBox.width);
    sqlite3_bind_int(stmt, 10, result.boundingBox.height);
    sqlite3_bind_text(stmt, 11, result.description.c_str(), -1, SQLITE_TRANSIENT);

    // 将时间戳转换为字符串
    auto timeT = std::chrono::system_clock::to_time_t(result.timestamp);
    std::stringstream ss;
    ss << std::put_time(std::gmtime(&timeT), "%Y-%m-%dT%H:%M:%SZ");
    std::string timestampStr = ss.str();
    sqlite3_bind_text(stmt, 12, timestampStr.c_str(), -1, SQLITE_TRANSIENT);
    
    sqlResult = sqlite3_step(stmt);
    bool success = (sqlResult == SQLITE_DONE);
    
    if (!success) {
        logDatabaseError("插入损伤结果", sqlite3_errmsg(db_));
    }
    
    sqlite3_finalize(stmt);
    return success;
}

std::string DatabaseManager::damageTypeToString(DamageType type) {
    switch (type) {
        case DamageType::CRACK: return "CRACK";
        case DamageType::WEAR: return "WEAR";
        case DamageType::SCRATCH: return "SCRATCH";
        case DamageType::PIT: return "PIT";
        case DamageType::BULGE: return "BULGE";
        case DamageType::AGING: return "AGING";
        case DamageType::INSTALL_DAMAGE: return "INSTALL_DAMAGE";
        default: return "NONE";
    }
}

std::string DatabaseManager::getCurrentTimeString() {
    auto now = std::chrono::system_clock::now();
    auto timeT = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

    std::stringstream ss;
    ss << std::put_time(std::gmtime(&timeT), "%Y-%m-%dT%H:%M:%S");
    ss << "." << std::setfill('0') << std::setw(3) << ms.count() << "Z";
    return ss.str();
}

void DatabaseManager::logDatabaseError(const std::string& operation, const std::string& errorMsg) {
    std::string fullMsg = "数据库操作失败 [" + operation + "]";
    if (!errorMsg.empty()) {
        fullMsg += ": " + errorMsg;
    }
    Utils::logError(fullMsg);
}

bool DatabaseManager::isConnected() const {
    std::lock_guard<std::mutex> lock(dbMutex_);
    return db_ != nullptr && isInitialized_;
}

std::vector<DatabaseManager::DetectionSession> DatabaseManager::getRecentSessions(int cameraId, int limit) {
    std::vector<DetectionSession> sessions;

    if (!isInitialized_) {
        return sessions;
    }

    std::lock_guard<std::mutex> lock(dbMutex_);

    const char* sql = R"(
        SELECT id, camera_id, timestamp, damage_count, created_at
        FROM detection_sessions
        WHERE camera_id = ?
        ORDER BY created_at DESC
        LIMIT ?;
    )";

    sqlite3_stmt* stmt;
    int result = sqlite3_prepare_v2(db_, sql, -1, &stmt, nullptr);

    if (result != SQLITE_OK) {
        logDatabaseError("准备查询会话语句", sqlite3_errmsg(db_));
        return sessions;
    }

    sqlite3_bind_int(stmt, 1, cameraId);
    sqlite3_bind_int(stmt, 2, limit);

    while (sqlite3_step(stmt) == SQLITE_ROW) {
        DetectionSession session;
        session.id = sqlite3_column_int(stmt, 0);
        session.cameraId = sqlite3_column_int(stmt, 1);
        const char* timestampText = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 2));
        session.timestamp = timestampText ? std::string(timestampText) : "";
        session.damageCount = sqlite3_column_int(stmt, 3);
        const char* createdAtText = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 4));
        session.createdAt = createdAtText ? std::string(createdAtText) : "";
        sessions.push_back(session);
    }

    sqlite3_finalize(stmt);
    return sessions;
}

std::vector<DatabaseManager::DatabaseDamageResult> DatabaseManager::getDamageResults(int sessionId) {
    std::vector<DatabaseDamageResult> results;

    if (!isInitialized_) {
        return results;
    }

    std::lock_guard<std::mutex> lock(dbMutex_);

    const char* sql = R"(
        SELECT id, session_id, damage_type, confidence, size_mm, center_x, center_y,
               bbox_x, bbox_y, bbox_width, bbox_height, description, timestamp
        FROM damage_results
        WHERE session_id = ?
        ORDER BY id;
    )";

    sqlite3_stmt* stmt;
    int result = sqlite3_prepare_v2(db_, sql, -1, &stmt, nullptr);

    if (result != SQLITE_OK) {
        logDatabaseError("准备查询损伤结果语句", sqlite3_errmsg(db_));
        return results;
    }

    sqlite3_bind_int(stmt, 1, sessionId);

    while (sqlite3_step(stmt) == SQLITE_ROW) {
        DatabaseDamageResult damageResult;
        damageResult.id = sqlite3_column_int(stmt, 0);
        damageResult.sessionId = sqlite3_column_int(stmt, 1);
        const char* typeText = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 2));
        damageResult.damageType = typeText ? std::string(typeText) : "";
        damageResult.confidence = sqlite3_column_double(stmt, 3);
        damageResult.sizeMm = sqlite3_column_double(stmt, 4);
        damageResult.centerX = sqlite3_column_double(stmt, 5);
        damageResult.centerY = sqlite3_column_double(stmt, 6);
        damageResult.bboxX = sqlite3_column_int(stmt, 7);
        damageResult.bboxY = sqlite3_column_int(stmt, 8);
        damageResult.bboxWidth = sqlite3_column_int(stmt, 9);
        damageResult.bboxHeight = sqlite3_column_int(stmt, 10);
        const char* descText = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 11));
        damageResult.description = descText ? std::string(descText) : "";
        const char* timestampText = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 12));
        damageResult.timestamp = timestampText ? std::string(timestampText) : "";
        results.push_back(damageResult);
    }

    sqlite3_finalize(stmt);
    return results;
}

bool DatabaseManager::cleanupOldData(int daysToKeep) {
    if (!isInitialized_) {
        return false;
    }

    std::lock_guard<std::mutex> lock(dbMutex_);

    std::string sql;
    if (daysToKeep <= 0) {
        // 如果保留天数为0或负数，删除所有数据
        sql = "DELETE FROM detection_sessions;";
    } else {
        sql = "DELETE FROM detection_sessions WHERE created_at < datetime('now', '-" +
              std::to_string(daysToKeep) + " days');";
    }

    sqlite3_stmt* stmt;
    int result = sqlite3_prepare_v2(db_, sql.c_str(), -1, &stmt, nullptr);

    if (result != SQLITE_OK) {
        logDatabaseError("准备清理数据语句", sqlite3_errmsg(db_));
        return false;
    }

    // 只有在使用参数化查询时才绑定参数
    if (daysToKeep > 0) {
        // 对于参数化查询，我们需要重新实现
        sqlite3_finalize(stmt);
        const char* paramSql = "DELETE FROM detection_sessions WHERE created_at < datetime('now', '-' || ? || ' days');";
        result = sqlite3_prepare_v2(db_, paramSql, -1, &stmt, nullptr);
        if (result != SQLITE_OK) {
            logDatabaseError("准备清理数据语句", sqlite3_errmsg(db_));
            return false;
        }
        sqlite3_bind_int(stmt, 1, daysToKeep);
    }

    result = sqlite3_step(stmt);
    bool success = (result == SQLITE_DONE);

    if (success) {
        int deletedRows = sqlite3_changes(db_);
        Utils::logInfo("清理了 " + std::to_string(deletedRows) + " 条旧数据记录");
    } else {
        logDatabaseError("清理旧数据", sqlite3_errmsg(db_));
    }

    sqlite3_finalize(stmt);
    return success;
}
