#include "../include/damage_detection_engine.h"
#include "../include/database_manager.h"
#ifdef USE_OPENCV
#include <chrono>
#include <fstream>
#include <iomanip>
#include <sstream>

DamageDetectionEngine::DamageDetectionEngine() : initialized(false) {
    stats.reset();
    databaseManager_ = std::make_unique<DatabaseManager>();
}

DamageDetectionEngine::~DamageDetectionEngine() {
    stop();
}

bool DamageDetectionEngine::initialize() {
    Utils::logInfo("初始化缺损检测引擎...");

    try {
        // 初始化算法参数（可以从配置文件加载）
        // 这里使用默认参数

        // 创建输出目录
        Utils::createDirectory("output/detection_results");
        Utils::createDirectory("output/detection_images");

        // 初始化数据库管理器
        if (!databaseManager_->initialize()) {
            Utils::logError("数据库管理器初始化失败");
            return false;
        }

        initialized = true;
        Utils::logInfo("缺损检测引擎初始化完成");
        return true;
    } catch (const std::exception& e) {
        Utils::logError("缺损检测引擎初始化失败: " + std::string(e.what()));
        return false;
    }
}

void DamageDetectionEngine::stop() {
    if (initialized) {
        Utils::logInfo("停止缺损检测引擎...");
        initialized = false;
    }
}

std::vector<DamageResult> DamageDetectionEngine::detectDamage(const cv::Mat& image, int cameraId) {
    if (!initialized || image.empty()) {
        return {};
    }
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    std::vector<DamageResult> allResults;
    
    try {
        // 图像预处理
        cv::Mat processedImage = preprocessImage(image);
        
        // 执行各种缺损检测算法
        auto crackResults = detectCracks(processedImage);
        auto wearResults = detectWear(processedImage);
        auto scratchResults = detectScratches(processedImage);
        auto dentResults = detectDents(processedImage);
        auto bulgeResults = detectBulges(processedImage);
        auto agingResults = detectAging(processedImage);
        auto installResults = detectInstallationDamage(processedImage);
        
        // 合并所有结果
        allResults.insert(allResults.end(), crackResults.begin(), crackResults.end());
        allResults.insert(allResults.end(), wearResults.begin(), wearResults.end());
        allResults.insert(allResults.end(), scratchResults.begin(), scratchResults.end());
        allResults.insert(allResults.end(), dentResults.begin(), dentResults.end());
        allResults.insert(allResults.end(), bulgeResults.begin(), bulgeResults.end());
        allResults.insert(allResults.end(), agingResults.begin(), agingResults.end());
        allResults.insert(allResults.end(), installResults.begin(), installResults.end());
        
        // 后处理结果
        allResults = postprocessResults(allResults);
        
        // 为每个结果生成描述
        for (auto& result : allResults) {
            result.description = generateDescription(result);
        }
        
    } catch (const std::exception& e) {
        Utils::logError("缺损检测异常: " + std::string(e.what()));
    }
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto processingTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();
    
    // 更新统计信息
    updateStats(allResults, processingTime);
    
    return allResults;
}

bool DamageDetectionEngine::saveResults(const std::vector<DamageResult>& results, int cameraId) {
    if (results.empty()) {
        return true;
    }

    try {
        // 使用数据库管理器保存结果
        if (!databaseManager_->saveDetectionResults(results, cameraId)) {
            Utils::logError("保存检测结果到数据库失败");
            return false;
        }

        Utils::logInfo("检测结果已保存到数据库，摄像头ID: " + std::to_string(cameraId) +
                      "，损伤数量: " + std::to_string(results.size()));
        return true;

    } catch (const std::exception& e) {
        Utils::logError("保存检测结果失败: " + std::string(e.what()));
        return false;
    }
}

ImageQuality DamageDetectionEngine::assessImageQuality(const cv::Mat& image) {
    ImageQuality quality;
    
    if (image.empty()) {
        return quality;
    }
    
    try {
        // 转换为灰度图
        cv::Mat gray;
        if (image.channels() == 3) {
            cv::cvtColor(image, gray, cv::COLOR_BGR2GRAY);
        } else {
            gray = image.clone();
        }
        
        // 计算清晰度（使用拉普拉斯算子的方差）
        cv::Mat laplacian;
        cv::Laplacian(gray, laplacian, CV_64F);
        cv::Scalar mean, stddev;
        cv::meanStdDev(laplacian, mean, stddev);
        quality.sharpness = stddev[0] * stddev[0] / 10000.0; // 归一化到0-1
        quality.sharpness = std::min(1.0, quality.sharpness);
        
        // 计算亮度
        cv::Scalar meanBrightness = cv::mean(gray);
        quality.brightness = meanBrightness[0] / 255.0;
        
        // 计算对比度
        cv::meanStdDev(gray, mean, stddev);
        quality.contrast = stddev[0] / 128.0; // 归一化到0-1
        quality.contrast = std::min(1.0, quality.contrast);
        
        // 判断图像质量是否合格
        quality.isGoodQuality = (quality.sharpness > 0.1) && 
                               (quality.brightness > 0.2 && quality.brightness < 0.8) &&
                               (quality.contrast > 0.2);
        
    } catch (const std::exception& e) {
        Utils::logError("图像质量评估异常: " + std::string(e.what()));
    }
    
    return quality;
}

cv::Mat DamageDetectionEngine::preprocessImage(const cv::Mat& image) {
    cv::Mat processed;
    
    try {
        // 转换为灰度图
        if (image.channels() == 3) {
            cv::cvtColor(image, processed, cv::COLOR_BGR2GRAY);
        } else {
            processed = image.clone();
        }
        
        // 高斯滤波去噪
        cv::GaussianBlur(processed, processed, cv::Size(3, 3), 1.0);
        
        // 直方图均衡化增强对比度
        cv::equalizeHist(processed, processed);
        
    } catch (const std::exception& e) {
        Utils::logError("图像预处理异常: " + std::string(e.what()));
        processed = image.clone();
    }
    
    return processed;
}

std::vector<DamageResult> DamageDetectionEngine::detectCracks(const cv::Mat& image) {
    std::vector<DamageResult> results;
    
    try {
        // 使用Canny边缘检测
        cv::Mat edges;
        cv::Canny(image, edges, 50, 150);
        
        // 查找轮廓
        std::vector<std::vector<cv::Point>> contours;
        cv::findContours(edges, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);
        
        for (const auto& contour : contours) {
            // 计算轮廓的长度和宽度比
            double length = cv::arcLength(contour, false);
            cv::Rect boundingRect = cv::boundingRect(contour);
            double aspectRatio = (double)boundingRect.width / boundingRect.height;
            
            // 判断是否为裂缝（长度大于阈值，宽度较小）
            if (length > params.crackMinLength && 
                (aspectRatio > 3.0 || aspectRatio < 0.33) &&
                boundingRect.width < params.crackMaxWidth) {
                
                DamageResult result;
                result.type = DamageType::CRACK;
                result.boundingBox = boundingRect;
                result.center = cv::Point2f(boundingRect.x + boundingRect.width/2.0f,
                                          boundingRect.y + boundingRect.height/2.0f);
                result.confidence = 0.7; // 简化的置信度计算
                result.size_mm = calculateSizeInMm(length);
                
                results.push_back(result);
            }
        }
        
    } catch (const std::exception& e) {
        Utils::logError("裂缝检测异常: " + std::string(e.what()));
    }
    
    return results;
}

std::vector<DamageResult> DamageDetectionEngine::detectWear(const cv::Mat& image) {
    std::vector<DamageResult> results;

    try {
        // 简化的磨损检测：基于纹理分析
        cv::Mat blurred;
        cv::GaussianBlur(image, blurred, cv::Size(15, 15), 5.0);

        cv::Mat diff;
        cv::absdiff(image, blurred, diff);

        // 阈值化
        cv::Mat thresh;
        cv::threshold(diff, thresh, 30, 255, cv::THRESH_BINARY);

        // 查找连通区域
        std::vector<std::vector<cv::Point>> contours;
        cv::findContours(thresh, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);

        for (const auto& contour : contours) {
            double area = cv::contourArea(contour);
            if (area > params.wearAreaThreshold) {
                DamageResult result;
                result.type = DamageType::WEAR;
                result.boundingBox = cv::boundingRect(contour);
                result.center = cv::Point2f(result.boundingBox.x + result.boundingBox.width/2.0f,
                                          result.boundingBox.y + result.boundingBox.height/2.0f);
                result.confidence = 0.6;
                result.size_mm = calculateSizeInMm(std::sqrt(area));

                results.push_back(result);
            }
        }
    } catch (const std::exception& e) {
        Utils::logError("磨损检测异常: " + std::string(e.what()));
    }

    return results;
}

std::vector<DamageResult> DamageDetectionEngine::detectScratches(const cv::Mat& image) {
    // 刮伤检测类似于裂缝检测，但参数不同
    return detectCracks(image); // 简化实现
}

std::vector<DamageResult> DamageDetectionEngine::detectDents(const cv::Mat& image) {
    std::vector<DamageResult> results;

    try {
        // 简化的凹坑检测：基于形态学操作
        cv::Mat kernel = cv::getStructuringElement(cv::MORPH_ELLIPSE, cv::Size(5, 5));
        cv::Mat opened;
        cv::morphologyEx(image, opened, cv::MORPH_OPEN, kernel);

        cv::Mat diff;
        cv::absdiff(image, opened, diff);

        cv::Mat thresh;
        cv::threshold(diff, thresh, 20, 255, cv::THRESH_BINARY);

        std::vector<std::vector<cv::Point>> contours;
        cv::findContours(thresh, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);

        for (const auto& contour : contours) {
            double area = cv::contourArea(contour);
            if (area > params.dentMinArea && area < params.dentMaxArea) {
                DamageResult result;
                result.type = DamageType::PIT;
                result.boundingBox = cv::boundingRect(contour);
                result.center = cv::Point2f(result.boundingBox.x + result.boundingBox.width/2.0f,
                                          result.boundingBox.y + result.boundingBox.height/2.0f);
                result.confidence = 0.5;
                result.size_mm = calculateSizeInMm(std::sqrt(area));

                results.push_back(result);
            }
        }
    } catch (const std::exception& e) {
        Utils::logError("凹坑检测异常: " + std::string(e.what()));
    }

    return results;
}

std::vector<DamageResult> DamageDetectionEngine::detectBulges(const cv::Mat& image) {
    // 鼓包检测类似于凹坑检测，但使用不同的形态学操作
    std::vector<DamageResult> results;

    try {
        cv::Mat kernel = cv::getStructuringElement(cv::MORPH_ELLIPSE, cv::Size(5, 5));
        cv::Mat closed;
        cv::morphologyEx(image, closed, cv::MORPH_CLOSE, kernel);

        cv::Mat diff;
        cv::absdiff(closed, image, diff);

        cv::Mat thresh;
        cv::threshold(diff, thresh, 20, 255, cv::THRESH_BINARY);

        std::vector<std::vector<cv::Point>> contours;
        cv::findContours(thresh, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);

        for (const auto& contour : contours) {
            double area = cv::contourArea(contour);
            if (area > params.bulgeMinArea && area < params.bulgeMaxArea) {
                DamageResult result;
                result.type = DamageType::BULGE;
                result.boundingBox = cv::boundingRect(contour);
                result.center = cv::Point2f(result.boundingBox.x + result.boundingBox.width/2.0f,
                                          result.boundingBox.y + result.boundingBox.height/2.0f);
                result.confidence = 0.5;
                result.size_mm = calculateSizeInMm(std::sqrt(area));

                results.push_back(result);
            }
        }
    } catch (const std::exception& e) {
        Utils::logError("鼓包检测异常: " + std::string(e.what()));
    }

    return results;
}

std::vector<DamageResult> DamageDetectionEngine::detectAging(const cv::Mat& image) {
    // 老化检测的简化实现
    std::vector<DamageResult> results;
    // 这里可以添加基于颜色和纹理变化的老化检测算法
    return results;
}

std::vector<DamageResult> DamageDetectionEngine::detectInstallationDamage(const cv::Mat& image) {
    // 安装破损检测的简化实现
    std::vector<DamageResult> results;
    // 这里可以添加基于几何形状异常的安装破损检测算法
    return results;
}

std::vector<DamageResult> DamageDetectionEngine::postprocessResults(const std::vector<DamageResult>& results) {
    std::vector<DamageResult> processed = results;

    // 去除重叠的检测结果
    for (size_t i = 0; i < processed.size(); ++i) {
        for (size_t j = i + 1; j < processed.size(); ) {
            // 计算两个边界框的重叠度
            cv::Rect intersection = processed[i].boundingBox & processed[j].boundingBox;
            double overlapRatio = (double)intersection.area() /
                                std::min(processed[i].boundingBox.area(), processed[j].boundingBox.area());

            if (overlapRatio > 0.5) {
                // 保留置信度更高的结果
                if (processed[i].confidence >= processed[j].confidence) {
                    processed.erase(processed.begin() + j);
                } else {
                    processed[i] = processed[j];
                    processed.erase(processed.begin() + j);
                }
            } else {
                ++j;
            }
        }
    }

    return processed;
}

void DamageDetectionEngine::updateStats(const std::vector<DamageResult>& results, double processingTime) {
    stats.totalFramesProcessed++;
    stats.totalDamagesDetected += results.size();

    // 更新平均处理时间
    stats.averageProcessingTime_ms = (stats.averageProcessingTime_ms * (stats.totalFramesProcessed - 1) +
                                     processingTime) / stats.totalFramesProcessed;

    // 统计各类型缺损数量
    for (const auto& result : results) {
        stats.damageTypeCounts[result.type]++;
    }

    // 更新平均置信度
    if (!results.empty()) {
        double totalConfidence = 0.0;
        for (const auto& result : results) {
            totalConfidence += result.confidence;
        }
        double frameAvgConfidence = totalConfidence / results.size();

        stats.averageConfidence = (stats.averageConfidence * (stats.totalFramesProcessed - 1) +
                                  frameAvgConfidence) / stats.totalFramesProcessed;
    }
}

double DamageDetectionEngine::calculateSizeInMm(double pixelSize) {
    return Utils::pixelToMm(pixelSize);
}

std::string DamageDetectionEngine::generateDescription(const DamageResult& result) {
    std::stringstream ss;
    ss << Utils::damageTypeToString(result.type);
    ss << " (尺寸: " << std::fixed << std::setprecision(2) << result.size_mm << "mm";
    ss << ", 置信度: " << std::setprecision(1) << (result.confidence * 100) << "%)";
    return ss.str();
}

#endif // USE_OPENCV
