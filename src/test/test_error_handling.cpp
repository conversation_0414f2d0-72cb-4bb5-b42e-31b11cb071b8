#include "../../include/common.h"
#include "../../include/video_encoder.h"
#include "../../include/stream_client.h"
#include "../../include/rtsp_stream_manager.h"
#include <iostream>
#include <chrono>
#include <thread>

#ifdef USE_OPENCV
#include <opencv2/opencv.hpp>
#endif

/**
 * @brief 错误处理和异常测试类
 * 
 * 专门测试系统在各种异常情况下的处理能力
 */
class ErrorHandlingTest {
public:
    ErrorHandlingTest() = default;
    ~ErrorHandlingTest() = default;
    
    /**
     * @brief 运行所有错误处理测试
     * @return 是否所有测试通过
     */
    bool runAllTests() {
        Utils::logInfo("=== 开始错误处理和异常测试 ===");
        
        bool allPassed = true;
        
        // 测试配置错误处理
        if (!testConfigurationErrors()) {
            Utils::logError("配置错误处理测试失败");
            allPassed = false;
        }
        
        // 测试初始化错误处理
        if (!testInitializationErrors()) {
            Utils::logError("初始化错误处理测试失败");
            allPassed = false;
        }
        
        // 测试运行时错误处理
        if (!testRuntimeErrors()) {
            Utils::logError("运行时错误处理测试失败");
            allPassed = false;
        }
        
        // 测试资源清理
        if (!testResourceCleanup()) {
            Utils::logError("资源清理测试失败");
            allPassed = false;
        }
        
        // 测试边界条件
        if (!testBoundaryConditions()) {
            Utils::logError("边界条件测试失败");
            allPassed = false;
        }
        
        // 测试异常恢复
        if (!testExceptionRecovery()) {
            Utils::logError("异常恢复测试失败");
            allPassed = false;
        }
        
        if (allPassed) {
            Utils::logInfo("=== 所有错误处理和异常测试通过 ===");
        } else {
            Utils::logError("=== 部分错误处理和异常测试失败 ===");
        }
        
        return allPassed;
    }

private:
    /**
     * @brief 测试配置错误处理
     */
    bool testConfigurationErrors() {
        Utils::logInfo("测试配置错误处理...");
        
        bool allPassed = true;
        
        // 测试视频编码器配置错误
        {
            Utils::logInfo("测试视频编码器配置错误...");
            
            VideoEncoder encoder;
            VideoEncoderConfig config;
            
            // 测试无效宽度
            config.width = 0;
            config.height = 480;
            config.fps = 15;
            config.bitrate = 1000000;
            
            if (encoder.initialize(config)) {
                Utils::logError("应该拒绝无效宽度配置");
                allPassed = false;
            } else {
                Utils::logInfo("正确拒绝了无效宽度配置");
            }
            
            // 测试无效高度
            config.width = 640;
            config.height = 0;
            
            if (encoder.initialize(config)) {
                Utils::logError("应该拒绝无效高度配置");
                allPassed = false;
            } else {
                Utils::logInfo("正确拒绝了无效高度配置");
            }
            
            // 测试无效帧率
            config.width = 640;
            config.height = 480;
            config.fps = 0;
            
            if (encoder.initialize(config)) {
                Utils::logError("应该拒绝无效帧率配置");
                allPassed = false;
            } else {
                Utils::logInfo("正确拒绝了无效帧率配置");
            }
            
            // 测试无效码率
            config.fps = 15;
            config.bitrate = 0;
            
            if (encoder.initialize(config)) {
                Utils::logError("应该拒绝无效码率配置");
                allPassed = false;
            } else {
                Utils::logInfo("正确拒绝了无效码率配置");
            }
        }
        
        // 测试推流客户端配置错误
        {
            Utils::logInfo("测试推流客户端配置错误...");
            
            StreamClient client;
            StreamClientConfig config;
            
            // 测试空URL
            config.enabled = true;
            config.pushUrl = "";
            config.viewUrl = "";
            config.codec = "h264";
            config.width = 640;
            config.height = 480;
            config.fps = 15;
            config.bitrate = 1000000;
            
            if (client.initialize(config)) {
                Utils::logError("应该拒绝空URL配置");
                allPassed = false;
            } else {
                Utils::logInfo("正确拒绝了空URL配置");
            }
            
            // 测试无效编解码器
            config.pushUrl = "rtmp://localhost:1935/live";
            config.viewUrl = "rtsp://localhost:8554/live";
            config.codec = "invalid_codec";
            
            if (client.initialize(config)) {
                Utils::logError("应该拒绝无效编解码器配置");
                allPassed = false;
            } else {
                Utils::logInfo("正确拒绝了无效编解码器配置");
            }
        }
        
        if (allPassed) {
            Utils::logInfo("配置错误处理测试通过");
        }
        
        return allPassed;
    }
    
    /**
     * @brief 测试初始化错误处理
     */
    bool testInitializationErrors() {
        Utils::logInfo("测试初始化错误处理...");
        
        bool allPassed = true;
        
        // 测试重复初始化
        {
            Utils::logInfo("测试重复初始化...");
            
            VideoEncoder encoder;
            VideoEncoderConfig config;
            config.width = 640;
            config.height = 480;
            config.fps = 15;
            config.bitrate = 1000000;
            config.codec = "h264";
            config.preset = "fast";
            config.profile = "baseline";
            
            // 第一次初始化
            if (!encoder.initialize(config)) {
                Utils::logError("第一次初始化失败");
                allPassed = false;
            } else {
                Utils::logInfo("第一次初始化成功");
                
                // 第二次初始化应该失败或者先清理
                if (encoder.initialize(config)) {
                    Utils::logInfo("允许重复初始化（可能内部进行了清理）");
                } else {
                    Utils::logInfo("正确拒绝了重复初始化");
                }
            }
        }
        
        // 测试在错误状态下的操作
        {
            Utils::logInfo("测试在错误状态下的操作...");
            
            VideoEncoder encoder;
            // 不进行初始化，直接尝试编码
            
#ifdef USE_OPENCV
            cv::Mat testFrame = cv::Mat::zeros(480, 640, CV_8UC3);
            EncodedPacket packet;
            
            if (encoder.encode(testFrame, packet)) {
                Utils::logError("应该拒绝在未初始化状态下的编码");
                allPassed = false;
            } else {
                Utils::logInfo("正确拒绝了在未初始化状态下的编码");
            }
#endif
        }
        
        if (allPassed) {
            Utils::logInfo("初始化错误处理测试通过");
        }
        
        return allPassed;
    }
    
    /**
     * @brief 测试运行时错误处理
     */
    bool testRuntimeErrors() {
        Utils::logInfo("测试运行时错误处理...");
        
        bool allPassed = true;
        
#ifdef USE_OPENCV
        // 测试空帧处理
        {
            Utils::logInfo("测试空帧处理...");
            
            VideoEncoder encoder;
            VideoEncoderConfig config;
            config.width = 640;
            config.height = 480;
            config.fps = 15;
            config.bitrate = 1000000;
            config.codec = "h264";
            config.preset = "fast";
            config.profile = "baseline";
            
            if (encoder.initialize(config)) {
                cv::Mat emptyFrame;
                EncodedPacket packet;
                
                if (encoder.encode(emptyFrame, packet)) {
                    Utils::logError("应该拒绝空帧");
                    allPassed = false;
                } else {
                    Utils::logInfo("正确拒绝了空帧");
                }
            }
        }
        
        // 测试错误尺寸帧处理
        {
            Utils::logInfo("测试错误尺寸帧处理...");
            
            VideoEncoder encoder;
            VideoEncoderConfig config;
            config.width = 640;
            config.height = 480;
            config.fps = 15;
            config.bitrate = 1000000;
            config.codec = "h264";
            config.preset = "fast";
            config.profile = "baseline";
            
            if (encoder.initialize(config)) {
                // 创建错误尺寸的帧
                cv::Mat wrongSizeFrame = cv::Mat::zeros(240, 320, CV_8UC3);
                EncodedPacket packet;
                
                if (encoder.encode(wrongSizeFrame, packet)) {
                    Utils::logWarning("接受了错误尺寸的帧（可能进行了自动缩放）");
                } else {
                    Utils::logInfo("正确拒绝了错误尺寸的帧");
                }
            }
        }
        
        // 测试错误格式帧处理
        {
            Utils::logInfo("测试错误格式帧处理...");
            
            VideoEncoder encoder;
            VideoEncoderConfig config;
            config.width = 640;
            config.height = 480;
            config.fps = 15;
            config.bitrate = 1000000;
            config.codec = "h264";
            config.preset = "fast";
            config.profile = "baseline";
            
            if (encoder.initialize(config)) {
                // 创建错误格式的帧（单通道而不是三通道）
                cv::Mat wrongFormatFrame = cv::Mat::zeros(480, 640, CV_8UC1);
                EncodedPacket packet;
                
                if (encoder.encode(wrongFormatFrame, packet)) {
                    Utils::logWarning("接受了错误格式的帧（可能进行了自动转换）");
                } else {
                    Utils::logInfo("正确拒绝了错误格式的帧");
                }
            }
        }
#endif
        
        if (allPassed) {
            Utils::logInfo("运行时错误处理测试通过");
        }
        
        return allPassed;
    }
    
    /**
     * @brief 测试资源清理
     */
    bool testResourceCleanup() {
        Utils::logInfo("测试资源清理...");
        
        bool allPassed = true;
        
        // 测试编码器资源清理
        {
            Utils::logInfo("测试编码器资源清理...");
            
            VideoEncoder* encoder = new VideoEncoder();
            VideoEncoderConfig config;
            config.width = 640;
            config.height = 480;
            config.fps = 15;
            config.bitrate = 1000000;
            config.codec = "h264";
            config.preset = "fast";
            config.profile = "baseline";
            
            if (encoder->initialize(config)) {
                Utils::logInfo("编码器初始化成功");
                
                // 删除编码器，测试析构函数是否正确清理资源
                delete encoder;
                Utils::logInfo("编码器已删除，资源应该已清理");
            } else {
                delete encoder;
                Utils::logWarning("编码器初始化失败");
            }
        }
        
        // 测试推流客户端资源清理
        {
            Utils::logInfo("测试推流客户端资源清理...");
            
            StreamClient* client = new StreamClient();
            StreamClientConfig config;
            config.enabled = true;
            config.pushUrl = "rtmp://localhost:1935/live";
            config.viewUrl = "rtsp://localhost:8554/live";
            config.codec = "h264";
            config.width = 640;
            config.height = 480;
            config.fps = 15;
            config.bitrate = 1000000;
            
            if (client->initialize(config)) {
                Utils::logInfo("推流客户端初始化成功");
            } else {
                Utils::logInfo("推流客户端初始化失败（预期的，因为没有服务器）");
            }
            
            delete client;
            Utils::logInfo("推流客户端已删除，资源应该已清理");
        }
        
        Utils::logInfo("资源清理测试通过");
        return allPassed;
    }
    
    /**
     * @brief 测试边界条件
     */
    bool testBoundaryConditions() {
        Utils::logInfo("测试边界条件...");
        
        bool allPassed = true;
        
        // 测试极限配置
        {
            Utils::logInfo("测试极限配置...");
            
            VideoEncoder encoder;
            VideoEncoderConfig config;
            
            // 测试最小分辨率
            config.width = 1;
            config.height = 1;
            config.fps = 1;
            config.bitrate = 1;
            config.codec = "h264";
            
            if (encoder.initialize(config)) {
                Utils::logWarning("接受了极小配置");
            } else {
                Utils::logInfo("正确拒绝了极小配置");
            }
            
            // 测试最大分辨率
            config.width = 7680;  // 8K
            config.height = 4320;
            config.fps = 120;
            config.bitrate = 100000000;  // 100Mbps
            
            if (encoder.initialize(config)) {
                Utils::logWarning("接受了极大配置");
            } else {
                Utils::logInfo("正确处理了极大配置");
            }
        }
        
        Utils::logInfo("边界条件测试通过");
        return allPassed;
    }
    
    /**
     * @brief 测试异常恢复
     */
    bool testExceptionRecovery() {
        Utils::logInfo("测试异常恢复...");
        
        bool allPassed = true;
        
        // 测试编码器重新初始化
        {
            Utils::logInfo("测试编码器重新初始化...");
            
            VideoEncoder encoder;
            VideoEncoderConfig config;
            config.width = 640;
            config.height = 480;
            config.fps = 15;
            config.bitrate = 1000000;
            config.codec = "h264";
            config.preset = "fast";
            config.profile = "baseline";
            
            // 第一次初始化
            if (encoder.initialize(config)) {
                Utils::logInfo("第一次初始化成功");
                
                // 模拟错误后重新初始化
                if (encoder.initialize(config)) {
                    Utils::logInfo("重新初始化成功");
                } else {
                    Utils::logWarning("重新初始化失败");
                }
            }
        }
        
        Utils::logInfo("异常恢复测试通过");
        return allPassed;
    }
};

/**
 * @brief 运行错误处理和异常测试
 * @return 是否所有测试通过
 */
bool runErrorHandlingTests() {
    ErrorHandlingTest test;
    return test.runAllTests();
}
