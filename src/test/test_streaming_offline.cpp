#include "../../include/common.h"
#include "../../include/video_encoder.h"
#include "../../include/stream_client.h"
#include "../../include/rtsp_stream_manager.h"
#include <iostream>
#include <chrono>
#include <thread>
#include <fstream>

#ifdef USE_OPENCV
#include <opencv2/opencv.hpp>
#endif

/**
 * @brief 离线推流功能测试类
 * 
 * 专门用于测试推流功能的核心逻辑，不依赖实际的网络连接
 * 使用文件输出和模拟数据进行测试
 */
class OfflineStreamingTest {
public:
    OfflineStreamingTest() = default;
    ~OfflineStreamingTest() = default;
    
    /**
     * @brief 运行所有离线测试
     * @return 是否所有测试通过
     */
    bool runAllTests() {
        Utils::logInfo("=== 开始离线推流功能测试 ===");
        
        bool allPassed = true;
        
        // 测试视频编码器（文件输出）
        if (!testVideoEncoderOffline()) {
            Utils::logError("离线视频编码器测试失败");
            allPassed = false;
        }
        
        // 测试推流管理器配置
        if (!testStreamManagerConfig()) {
            Utils::logError("推流管理器配置测试失败");
            allPassed = false;
        }
        
        // 测试帧处理逻辑
        if (!testFrameProcessing()) {
            Utils::logError("帧处理逻辑测试失败");
            allPassed = false;
        }
        
        // 测试质量控制逻辑
        if (!testQualityControlLogic()) {
            Utils::logError("质量控制逻辑测试失败");
            allPassed = false;
        }
        
        // 测试错误处理
        if (!testErrorHandling()) {
            Utils::logError("错误处理测试失败");
            allPassed = false;
        }
        
        // 测试性能指标
        if (!testPerformanceMetrics()) {
            Utils::logError("性能指标测试失败");
            allPassed = false;
        }
        
        if (allPassed) {
            Utils::logInfo("=== 所有离线推流功能测试通过 ===");
        } else {
            Utils::logError("=== 部分离线推流功能测试失败 ===");
        }
        
        return allPassed;
    }

private:
    /**
     * @brief 测试视频编码器（文件输出）
     */
    bool testVideoEncoderOffline() {
        Utils::logInfo("测试离线视频编码器...");
        
#ifdef USE_FFMPEG
        VideoEncoder encoder;
        VideoEncoderConfig config;
        config.codec = "h264";
        config.width = 640;
        config.height = 480;
        config.fps = 15;
        config.bitrate = 1000000;
        config.preset = "fast";
        config.profile = "baseline";
        
        if (!encoder.initialize(config)) {
            Utils::logError("视频编码器初始化失败");
            return false;
        }
        
        Utils::logInfo("视频编码器初始化成功");
        
        // 测试编码器状态
        if (encoder.getStatus() != EncoderStatus::INITIALIZED) {
            Utils::logError("编码器状态错误");
            return false;
        }
        
        Utils::logInfo("编码器状态正确");
        
#ifdef USE_OPENCV
        // 创建测试帧
        cv::Mat testFrame = cv::Mat::zeros(480, 640, CV_8UC3);
        cv::rectangle(testFrame, cv::Rect(100, 100, 200, 200), cv::Scalar(0, 255, 0), -1);
        cv::putText(testFrame, "Test Frame", cv::Point(50, 50), cv::FONT_HERSHEY_SIMPLEX, 1, cv::Scalar(255, 255, 255), 2);
        
        // 测试编码
        EncodedPacket packet;
        if (!encoder.encode(testFrame, packet)) {
            Utils::logError("帧编码失败");
            return false;
        }
        
        Utils::logInfo("帧编码成功");
        
        // 验证编码后的数据
        // 注意：第一帧可能需要多次调用才能产生输出
        if (packet.size == 0) {
            Utils::logInfo("第一帧编码可能需要缓冲，尝试编码更多帧...");

            // 尝试编码更多帧
            for (int i = 0; i < 5; ++i) {
                cv::Mat nextFrame = cv::Mat::zeros(480, 640, CV_8UC3);
                cv::rectangle(nextFrame, cv::Rect(100 + i*10, 100 + i*10, 200, 200), cv::Scalar(0, 255, 0), -1);

                EncodedPacket nextPacket;
                if (encoder.encode(nextFrame, nextPacket) && nextPacket.size > 0) {
                    Utils::logInfo("编码成功，数据大小: " + std::to_string(nextPacket.size) + " 字节");
                    break;
                }
            }
        }
        
        Utils::logInfo("编码数据验证成功，大小: " + std::to_string(packet.size) + " 字节");
#endif
        
        Utils::logInfo("离线视频编码器测试通过");
        return true;
#else
        Utils::logInfo("FFmpeg未启用，跳过离线视频编码器测试");
        return true;
#endif
    }
    
    /**
     * @brief 测试推流管理器配置
     */
    bool testStreamManagerConfig() {
        Utils::logInfo("测试推流管理器配置...");
        
        RTSPStreamManager manager;
        
        // 测试配置验证
        RTSPStreamConfig config;
        config.enabled = true;
        config.serverAddress = "localhost";
        config.port = 8554;
        config.streamPath = "/test";
        config.codec = "h264";
        config.width = 640;
        config.height = 480;
        config.fps = 15;
        config.bitrate = 1000000;
        config.preset = "fast";
        config.bufferSize = 30;
        config.maxClients = 10;
        
        // 注意：这里我们只测试配置验证，不实际初始化网络组件
        Utils::logInfo("配置验证: 编解码器=" + config.codec);
        Utils::logInfo("配置验证: 分辨率=" + std::to_string(config.width) + "x" + std::to_string(config.height));
        Utils::logInfo("配置验证: 帧率=" + std::to_string(config.fps));
        Utils::logInfo("配置验证: 码率=" + std::to_string(config.bitrate));
        
        // 验证配置参数的合理性
        if (config.width <= 0 || config.height <= 0) {
            Utils::logError("无效的分辨率配置");
            return false;
        }
        
        if (config.fps <= 0 || config.fps > 120) {
            Utils::logError("无效的帧率配置");
            return false;
        }
        
        if (config.bitrate <= 0) {
            Utils::logError("无效的码率配置");
            return false;
        }
        
        Utils::logInfo("推流管理器配置测试通过");
        return true;
    }
    
    /**
     * @brief 测试帧处理逻辑
     */
    bool testFrameProcessing() {
        Utils::logInfo("测试帧处理逻辑...");
        
#ifdef USE_OPENCV
        // 创建不同类型的测试帧
        std::vector<cv::Mat> testFrames;
        
        // 正常帧
        cv::Mat normalFrame = cv::Mat::zeros(480, 640, CV_8UC3);
        cv::rectangle(normalFrame, cv::Rect(50, 50, 100, 100), cv::Scalar(255, 0, 0), -1);
        testFrames.push_back(normalFrame);
        
        // 空帧
        cv::Mat emptyFrame;
        testFrames.push_back(emptyFrame);
        
        // 不同尺寸的帧
        cv::Mat smallFrame = cv::Mat::zeros(240, 320, CV_8UC3);
        testFrames.push_back(smallFrame);
        
        cv::Mat largeFrame = cv::Mat::zeros(1080, 1920, CV_8UC3);
        testFrames.push_back(largeFrame);
        
        int validFrames = 0;
        int invalidFrames = 0;
        
        for (size_t i = 0; i < testFrames.size(); ++i) {
            const cv::Mat& frame = testFrames[i];
            
            // 模拟帧验证逻辑
            if (frame.empty()) {
                Utils::logInfo("检测到空帧 (索引 " + std::to_string(i) + ")");
                invalidFrames++;
                continue;
            }
            
            if (frame.cols <= 0 || frame.rows <= 0) {
                Utils::logInfo("检测到无效尺寸帧 (索引 " + std::to_string(i) + ")");
                invalidFrames++;
                continue;
            }
            
            Utils::logInfo("有效帧 (索引 " + std::to_string(i) + "): " + 
                          std::to_string(frame.cols) + "x" + std::to_string(frame.rows));
            validFrames++;
        }
        
        Utils::logInfo("帧处理统计: 有效帧=" + std::to_string(validFrames) + 
                      ", 无效帧=" + std::to_string(invalidFrames));
        
        if (validFrames == 0) {
            Utils::logError("没有检测到有效帧");
            return false;
        }
        
        Utils::logInfo("帧处理逻辑测试通过");
        return true;
#else
        Utils::logInfo("OpenCV未启用，跳过帧处理逻辑测试");
        return true;
#endif
    }
    
    /**
     * @brief 测试质量控制逻辑
     */
    bool testQualityControlLogic() {
        Utils::logInfo("测试质量控制逻辑...");
        
        // 模拟质量控制参数
        struct QualityParams {
            int targetFPS = 15;
            int currentFPS = 0;
            int targetBitrate = 2000000;
            int currentBitrate = 0;
            int droppedFrames = 0;
            int totalFrames = 0;
        } params;
        
        // 模拟帧率控制
        auto lastFrameTime = std::chrono::steady_clock::now();
        int frameInterval = 1000 / params.targetFPS; // ms

        for (int i = 0; i < 100; ++i) {
            // 模拟帧间隔
            std::this_thread::sleep_for(std::chrono::milliseconds(frameInterval + (i % 3) * 10));

            auto now = std::chrono::steady_clock::now();
            auto timeSinceLastFrame = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastFrameTime).count();

            if (timeSinceLastFrame >= frameInterval * 0.8) {
                // 接受帧
                params.totalFrames++;
                lastFrameTime = now;
            } else {
                // 丢弃帧
                params.droppedFrames++;
            }
        }
        
        // 计算统计信息
        double dropRate = (double)params.droppedFrames / (params.totalFrames + params.droppedFrames);
        
        Utils::logInfo("质量控制统计:");
        Utils::logInfo("  总帧数: " + std::to_string(params.totalFrames));
        Utils::logInfo("  丢弃帧数: " + std::to_string(params.droppedFrames));
        Utils::logInfo("  丢帧率: " + std::to_string(dropRate * 100) + "%");
        
        // 验证质量控制效果
        if (dropRate > 0.5) {
            Utils::logError("丢帧率过高");
            return false;
        }
        
        Utils::logInfo("质量控制逻辑测试通过");
        return true;
    }
    
    /**
     * @brief 测试错误处理
     */
    bool testErrorHandling() {
        Utils::logInfo("测试错误处理...");
        
        // 测试无效配置处理
        VideoEncoderConfig invalidConfig;
        invalidConfig.width = 0;  // 无效宽度
        invalidConfig.height = 480;
        invalidConfig.fps = 15;
        invalidConfig.bitrate = 1000000;
        
        VideoEncoder encoder;
        if (encoder.initialize(invalidConfig)) {
            Utils::logError("应该拒绝无效配置");
            return false;
        }
        
        Utils::logInfo("正确拒绝了无效配置");
        
        // 测试空指针处理等其他错误情况
        Utils::logInfo("错误处理测试通过");
        return true;
    }
    
    /**
     * @brief 测试性能指标
     */
    bool testPerformanceMetrics() {
        Utils::logInfo("测试性能指标...");
        
        // 模拟性能统计
        struct PerformanceStats {
            std::chrono::steady_clock::time_point startTime;
            int processedFrames = 0;
            size_t totalBytes = 0;
            double avgFPS = 0.0;
            double avgBitrate = 0.0;
        } stats;
        
        stats.startTime = std::chrono::steady_clock::now();
        
        // 模拟处理过程
        for (int i = 0; i < 50; ++i) {
            stats.processedFrames++;
            stats.totalBytes += 1024 * 50; // 假设每帧50KB
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
        
        auto endTime = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - stats.startTime).count();
        
        if (duration > 0) {
            stats.avgFPS = (double)stats.processedFrames * 1000.0 / duration;
            stats.avgBitrate = (double)stats.totalBytes * 8.0 * 1000.0 / duration; // bps
        }
        
        Utils::logInfo("性能统计:");
        Utils::logInfo("  处理帧数: " + std::to_string(stats.processedFrames));
        Utils::logInfo("  总字节数: " + std::to_string(stats.totalBytes));
        Utils::logInfo("  平均FPS: " + std::to_string(stats.avgFPS));
        Utils::logInfo("  平均码率: " + std::to_string(stats.avgBitrate / 1000000.0) + " Mbps");
        Utils::logInfo("  处理时间: " + std::to_string(duration) + " ms");
        
        // 验证性能指标的合理性
        if (stats.avgFPS <= 0) {
            Utils::logError("无效的FPS统计");
            return false;
        }
        
        Utils::logInfo("性能指标测试通过");
        return true;
    }
};

/**
 * @brief 运行离线推流功能测试
 * @return 是否所有测试通过
 */
bool runOfflineStreamingTests() {
    OfflineStreamingTest test;
    return test.runAllTests();
}
