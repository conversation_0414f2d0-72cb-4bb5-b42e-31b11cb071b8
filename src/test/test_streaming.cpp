#include "../../include/common.h"
#include "../../include/video_encoder.h"
#include "../../include/stream_client.h"
#include "../../include/rtsp_stream_manager.h"
#include <iostream>
#include <chrono>
#include <thread>

#ifdef USE_OPENCV
#include <opencv2/opencv.hpp>
#endif

/**
 * @brief 推流功能测试类
 */
class StreamingTest {
public:
    StreamingTest() = default;
    ~StreamingTest() = default;
    
    /**
     * @brief 运行所有测试
     * @return 是否所有测试通过
     */
    bool runAllTests() {
        Utils::logInfo("=== 开始推流功能测试 ===");
        
        bool allPassed = true;
        
        // 测试视频编码器
        if (!testVideoEncoder()) {
            Utils::logError("视频编码器测试失败");
            allPassed = false;
        }
        
        // 测试推流客户端
        if (!testStreamClient()) {
            Utils::logError("推流客户端测试失败");
            allPassed = false;
        }
        
        // 测试推流管理器
        if (!testStreamManager()) {
            Utils::logError("推流管理器测试失败");
            allPassed = false;
        }
        
        // 测试质量控制
        if (!testQualityControl()) {
            Utils::logError("质量控制测试失败");
            allPassed = false;
        }
        
        if (allPassed) {
            Utils::logInfo("=== 所有推流功能测试通过 ===");
        } else {
            Utils::logError("=== 部分推流功能测试失败 ===");
        }
        
        return allPassed;
    }

private:
    /**
     * @brief 测试视频编码器
     */
    bool testVideoEncoder() {
        Utils::logInfo("测试视频编码器...");
        
#ifdef USE_FFMPEG
        VideoEncoder encoder;
        
        // 测试初始化
        VideoEncoderConfig config;
        config.codec = "h264";
        config.width = 640;
        config.height = 480;
        config.fps = 15;
        config.bitrate = 1000000;
        config.preset = "fast";
        config.profile = "baseline";
        
        if (!encoder.initialize(config)) {
            Utils::logError("编码器初始化失败");
            return false;
        }
        
        Utils::logInfo("编码器初始化成功: " + encoder.getEncoderInfo());
        
        // 测试编码
#ifdef USE_OPENCV
        cv::Mat testFrame = cv::Mat::zeros(480, 640, CV_8UC3);
        cv::rectangle(testFrame, cv::Rect(100, 100, 200, 200), cv::Scalar(0, 255, 0), -1);
        
        EncodedPacket packet;
        if (!encoder.encode(testFrame, packet)) {
            Utils::logError("编码测试帧失败");
            return false;
        }
        
        Utils::logInfo("编码测试成功，数据包大小: " + std::to_string(packet.size) + " bytes");
#endif
        
        // 测试码率调整
        if (!encoder.adjustBitrate(2000000)) {
            Utils::logError("码率调整失败");
            return false;
        }
        
        int currentBitrate = encoder.getCurrentBitrate();
        if (currentBitrate != 2000000) {
            Utils::logError("码率调整验证失败: " + std::to_string(currentBitrate));
            return false;
        }
        
        Utils::logInfo("码率调整测试成功");
        
        // 测试重置
        encoder.reset();
        if (encoder.isInitialized()) {
            Utils::logError("编码器重置失败");
            return false;
        }
        
        Utils::logInfo("编码器重置测试成功");
        
#else
        Utils::logInfo("FFmpeg未启用，跳过编码器测试");
#endif
        
        return true;
    }
    
    /**
     * @brief 测试推流客户端
     */
    bool testStreamClient() {
        Utils::logInfo("测试推流客户端...");
        
#ifdef USE_FFMPEG
        StreamClient client;
        
        // 测试初始化
        StreamClientConfig config;
        config.enabled = true;
        config.pushUrl = "rtmp://localhost:1935/test";
        config.viewUrl = "rtsp://localhost:8554/test";
        config.codec = "h264";
        config.width = 640;
        config.height = 480;
        config.fps = 15;
        config.bitrate = 1000000;
        
        if (!client.initialize(config)) {
            Utils::logError("推流客户端初始化失败");
            return false;
        }
        
        Utils::logInfo("推流客户端初始化成功");
        Utils::logInfo("推流地址: " + client.getPushUrl());
        Utils::logInfo("观看地址: " + client.getViewUrl());
        
        // 测试配置更新
        config.bitrate = 2000000;
        if (!client.updateConfig(config)) {
            Utils::logError("配置更新失败");
            return false;
        }
        
        Utils::logInfo("配置更新测试成功");
        
        // 注意：实际连接测试需要外部RTSP服务器运行
        // 这里只测试基本功能
        
#else
        Utils::logInfo("FFmpeg未启用，跳过推流客户端测试");
#endif
        
        return true;
    }
    
    /**
     * @brief 测试推流管理器
     */
    bool testStreamManager() {
        Utils::logInfo("测试推流管理器...");
        
        RTSPStreamManager manager;
        
        // 测试初始化
        RTSPStreamConfig config;
        config.enabled = true;
        config.serverAddress = "localhost";
        config.port = 8554;
        config.streamPath = "/test";
        config.codec = "h264";
        config.width = 640;
        config.height = 480;
        config.fps = 15;
        config.bitrate = 1000000;
        config.preset = "fast";
        config.bufferSize = 30;
        config.maxClients = 10;
        
        if (!manager.initialize(config)) {
            Utils::logError("推流管理器初始化失败");
            return false;
        }
        
        Utils::logInfo("推流管理器初始化成功");
        Utils::logInfo("RTSP URL: " + manager.getRTSPUrl());
        
        // 测试状态检查
        if (manager.getStatus() != StreamStatus::STOPPED) {
            Utils::logError("初始状态错误");
            return false;
        }
        
        // 测试配置更新
        config.bitrate = 2000000;
        if (!manager.updateConfig(config)) {
            Utils::logError("配置更新失败");
            return false;
        }
        
        Utils::logInfo("配置更新测试成功");
        
        // 测试帧推送（不启动服务器）
#ifdef USE_OPENCV
        cv::Mat testFrame = cv::Mat::zeros(480, 640, CV_8UC3);
        cv::circle(testFrame, cv::Point(320, 240), 50, cv::Scalar(255, 0, 0), -1);
        
        // 推送帧应该失败，因为服务器未启动
        if (manager.pushFrame(testFrame, 0)) {
            Utils::logError("未启动状态下推送帧应该失败");
            return false;
        }
#endif
        
        Utils::logInfo("推流管理器基本功能测试成功");
        
        return true;
    }
    
    /**
     * @brief 测试质量控制
     */
    bool testQualityControl() {
        Utils::logInfo("测试质量控制功能...");
        
        // 创建测试用的推流管理器
        RTSPStreamManager manager;
        RTSPStreamConfig config;
        config.enabled = true;
        config.codec = "h264";
        config.width = 640;
        config.height = 480;
        config.fps = 15;
        config.bitrate = 2000000;
        config.bufferSize = 10; // 小缓冲区用于测试
        
        if (!manager.initialize(config)) {
            Utils::logError("质量控制测试初始化失败");
            return false;
        }
        
        // 测试统计信息
        const auto& stats = manager.getStats();
        if (stats.totalFrames.load() != 0) {
            Utils::logError("初始统计信息错误");
            return false;
        }
        
        Utils::logInfo("统计信息初始化正确");
        
        // 模拟高负载情况（快速推送大量帧）
#ifdef USE_OPENCV
        cv::Mat testFrame = cv::Mat::zeros(480, 640, CV_8UC3);
        
        // 推送大量帧来测试缓冲区管理
        for (int i = 0; i < 50; ++i) {
            cv::rectangle(testFrame, cv::Rect(i*10, i*5, 50, 50), 
                         cv::Scalar(i*5, 255-i*5, 128), -1);
            manager.pushFrame(testFrame, 0);
        }
        
        // 检查是否有帧被丢弃
        if (stats.droppedFrames.load() == 0) {
            Utils::logInfo("缓冲区管理正常，未发生丢帧");
        } else {
            Utils::logInfo("缓冲区管理正常，丢弃了 " + 
                          std::to_string(stats.droppedFrames.load()) + " 帧");
        }
#endif
        
        Utils::logInfo("质量控制功能测试成功");
        
        return true;
    }
};

/**
 * @brief 运行推流功能测试
 * @return 是否所有测试通过
 */
bool runStreamingTestsImpl() {
    StreamingTest test;
    return test.runAllTests();
}
