[INFO] 2025-07-31 14:23:56.562 - === 拉吊索缺损识别系统 - 测试模式 ===
[INFO] 2025-07-31 14:23:56.562 - === 开始运行所有测试 ===
[INFO] 2025-07-31 14:23:56.562 - 配置文件加载成功
[INFO] 2025-07-31 14:23:56.562 - 摄像头数量: 1
[INFO] 2025-07-31 14:23:56.562 - --- 基础功能测试 ---
[INFO] 2025-07-31 14:23:56.562 - 开始基础功能测试...
[INFO] 2025-07-31 14:23:56.562 - 运行基础功能测试...
[INFO] 2025-07-31 14:23:56.562 - 测试时间函数...
[INFO] 2025-07-31 14:23:56.562 - 当前时间: 2025-07-31 14:23:56.562
[INFO] 2025-07-31 14:23:56.562 - ✓ 时间函数测试通过
[INFO] 2025-07-31 14:23:56.562 - 测试损伤类型转换...
[INFO] 2025-07-31 14:23:56.562 - 损伤类型 0: 无损伤
[INFO] 2025-07-31 14:23:56.562 - 损伤类型 1: 裂缝
[INFO] 2025-07-31 14:23:56.562 - 损伤类型 2: 磨损
[INFO] 2025-07-31 14:23:56.562 - 损伤类型 3: 刮伤
[INFO] 2025-07-31 14:23:56.562 - 损伤类型 4: 凹坑
[INFO] 2025-07-31 14:23:56.563 - 损伤类型 5: 鼓包
[INFO] 2025-07-31 14:23:56.563 - 损伤类型 6: 老化
[INFO] 2025-07-31 14:23:56.563 - 损伤类型 7: 安装破损
[INFO] 2025-07-31 14:23:56.563 - ✓ 损伤类型转换测试通过
[INFO] 2025-07-31 14:23:56.563 - 测试单位转换...
[INFO] 2025-07-31 14:23:56.563 - 像素转换测试: 100.000000 pixels = 10.000000 mm = 100.000000 pixels
[INFO] 2025-07-31 14:23:56.563 - ✓ 单位转换测试通过
[INFO] 2025-07-31 14:23:56.563 - 测试目录创建...
[INFO] 2025-07-31 14:23:56.563 - ✓ 测试目录创建成功: output/test_basic
[INFO] 2025-07-31 14:23:56.563 - 测试工具函数...
[INFO] 2025-07-31 14:23:56.563 - 工具函数测试信息
[WARN] 2025-07-31 14:23:56.563 - 工具函数测试警告
[INFO] 2025-07-31 14:23:56.563 - ✓ 工具函数测试通过
[INFO] 2025-07-31 14:23:56.563 - 基础功能测试: 全部通过
[INFO] 2025-07-31 14:23:56.563 - ✓ 基础功能测试: 通过
[INFO] 2025-07-31 14:23:56.563 - --- 摄像头功能测试 ---
[INFO] 2025-07-31 14:23:56.563 - 开始摄像头功能测试...
[INFO] 2025-07-31 14:23:56.563 - 运行摄像头功能测试...
[INFO] 2025-07-31 14:23:56.563 - 测试摄像头检测...
[INFO] 2025-07-31 14:23:56.563 - 开始初始化摄像头管理器...
[INFO] 2025-07-31 14:23:56.563 - 开始检测可用摄像头...
[INFO] 2025-07-31 14:23:56.667 - ✓ 发现可访问的摄像头设备: /dev/video0
[INFO] 2025-07-31 14:23:56.668 - 摄像头检测完成，找到 1 个可用摄像头
[INFO] 2025-07-31 14:23:56.668 - 检测到 1 个可用摄像头
[INFO] 2025-07-31 14:23:56.668 - 初始化摄像头 0...
[INFO] 2025-07-31 14:23:56.668 - 摄像头 0 预初始化成功 (延迟打开)
[INFO] 2025-07-31 14:23:56.668 - 摄像头管理器初始化完成
[INFO] 2025-07-31 14:23:56.668 - 检测到 1 个摄像头
[INFO] 2025-07-31 14:23:56.668 - 摄像头 0: 可用
[INFO] 2025-07-31 14:23:56.668 - ✓ 摄像头检测测试通过
[INFO] 2025-07-31 14:23:56.668 - 停止摄像头采集...
[INFO] 2025-07-31 14:23:56.668 - 开始初始化摄像头管理器...
[INFO] 2025-07-31 14:23:56.668 - 开始检测可用摄像头...
[INFO] 2025-07-31 14:23:56.668 - ✓ 发现可访问的摄像头设备: /dev/video0
[INFO] 2025-07-31 14:23:56.668 - 摄像头检测完成，找到 1 个可用摄像头
[INFO] 2025-07-31 14:23:56.668 - 检测到 1 个可用摄像头
[INFO] 2025-07-31 14:23:56.668 - 初始化摄像头 0...
[INFO] 2025-07-31 14:23:56.668 - 摄像头 0 预初始化成功 (延迟打开)
[INFO] 2025-07-31 14:23:56.668 - 摄像头管理器初始化完成
[INFO] 2025-07-31 14:23:56.668 - 启动摄像头采集...
[INFO] 2025-07-31 14:23:56.668 - 正在打开摄像头 0...
[INFO] 2025-07-31 14:23:57.076 - ✓ 摄像头 0 启动成功 (640x480@30.000000fps)
[INFO] 2025-07-31 14:23:57.076 - 摄像头采集启动成功
[INFO] 2025-07-31 14:23:57.076 - 测试单摄像头采集...
[INFO] 2025-07-31 14:23:57.076 - 使用摄像头 0 进行测试
[INFO] 2025-07-31 14:23:57.428 - 保存样本图像: output/test_camera_single/sample_frame.jpg
[INFO] 2025-07-31 14:23:58.689 - 单摄像头测试结果:
[INFO] 2025-07-31 14:23:58.690 -   采集帧数: 20/20
[INFO] 2025-07-31 14:23:58.690 -   失败次数: 0
[INFO] 2025-07-31 14:23:58.690 -   测试时间: 1613 ms
[INFO] 2025-07-31 14:23:58.690 - ✓ 单摄像头采集测试通过
[INFO] 2025-07-31 14:23:58.690 - 测试多摄像头采集...
[INFO] 2025-07-31 14:23:58.690 - 配置的摄像头数量: 1
[INFO] 2025-07-31 14:23:58.690 - 实际可用摄像头数量: 1
[WARN] 2025-07-31 14:23:58.690 - 检测到单摄像头环境，跳过多摄像头采集测试
[INFO] 2025-07-31 14:23:58.690 - 建议: 连接多个摄像头或修改配置文件以启用多摄像头测试
[INFO] 2025-07-31 14:23:58.690 - ✓ 多摄像头测试: 跳过（单摄像头环境）
[INFO] 2025-07-31 14:23:58.690 - 测试所有摄像头采集功能...
[INFO] 2025-07-31 14:23:58.690 - 测试 getAllFrames() 方法在当前环境下的行为
[INFO] 2025-07-31 14:23:58.758 - 第 1 次采集: 获取到 1/1 个有效帧
[INFO] 2025-07-31 14:23:58.960 - 第 2 次采集: 获取到 1/1 个有效帧
[INFO] 2025-07-31 14:23:59.161 - 第 3 次采集: 获取到 1/1 个有效帧
[INFO] 2025-07-31 14:23:59.363 - 第 4 次采集: 获取到 1/1 个有效帧
[INFO] 2025-07-31 14:23:59.565 - 第 5 次采集: 获取到 1/1 个有效帧
[INFO] 2025-07-31 14:23:59.766 - 所有摄像头采集功能测试结果:
[INFO] 2025-07-31 14:23:59.766 -   配置摄像头数: 1
[INFO] 2025-07-31 14:23:59.766 -   测试次数: 5
[INFO] 2025-07-31 14:23:59.766 -   成功次数: 5
[INFO] 2025-07-31 14:23:59.766 -   成功率: 100.000000%
[INFO] 2025-07-31 14:23:59.766 -   平均有效帧数: 1.000000
[INFO] 2025-07-31 14:23:59.767 - 单摄像头环境评估: 符合预期
[INFO] 2025-07-31 14:23:59.767 - ✓ 所有摄像头采集功能测试通过
[INFO] 2025-07-31 14:23:59.767 - 测试图像质量...
[INFO] 2025-07-31 14:23:59.769 - 摄像头 0 图像质量: 合格
[INFO] 2025-07-31 14:23:59.769 - ✓ 图像质量测试通过
[INFO] 2025-07-31 14:23:59.769 - 停止摄像头采集...
[INFO] 2025-07-31 14:23:59.769 - 摄像头 0 停止采集
[INFO] 2025-07-31 14:23:59.769 - 摄像头采集已停止
[INFO] 2025-07-31 14:23:59.769 - 停止摄像头采集...
[INFO] 2025-07-31 14:23:59.771 - 摄像头功能测试: 全部通过
[INFO] 2025-07-31 14:23:59.771 - ✓ 摄像头功能测试: 通过
[INFO] 2025-07-31 14:23:59.771 - --- 系统集成测试 ---
[INFO] 2025-07-31 14:23:59.771 - 开始系统集成测试...
[INFO] 2025-07-31 14:23:59.771 - 运行系统集成测试...
[INFO] 2025-07-31 14:23:59.771 - 测试系统启动和关闭...
[INFO] 2025-07-31 14:23:59.771 - ✓ 系统启动关闭测试通过
[INFO] 2025-07-31 14:23:59.771 - 系统集成测试: 全部通过
[INFO] 2025-07-31 14:23:59.771 - ✓ 系统集成测试: 通过
[INFO] 2025-07-31 14:23:59.771 - --- 数据库功能测试 ---
[INFO] 2025-07-31 14:23:59.771 - 开始数据库功能测试...
[INFO] 2025-07-31 14:23:59.771 - 开始数据库功能测试...
[INFO] 2025-07-31 14:23:59.771 - 测试数据库初始化...
[INFO] 2025-07-31 14:23:59.776 - 数据库表和索引创建成功
[INFO] 2025-07-31 14:23:59.776 - 数据库初始化成功: output/test_database.db
[INFO] 2025-07-31 14:23:59.776 - ✓ 数据库初始化测试通过
[INFO] 2025-07-31 14:23:59.777 - 测试检测结果存储...
[INFO] 2025-07-31 14:23:59.782 - 数据库表和索引创建成功
[INFO] 2025-07-31 14:23:59.782 - 数据库初始化成功: output/test_storage.db
[INFO] 2025-07-31 14:23:59.783 - 检测结果已保存到数据库，会话ID: 1，损伤数量: 3
[INFO] 2025-07-31 14:23:59.784 - ✓ 检测结果存储测试通过
[INFO] 2025-07-31 14:23:59.784 - 测试数据查询功能...
[INFO] 2025-07-31 14:23:59.789 - 数据库表和索引创建成功
[INFO] 2025-07-31 14:23:59.789 - 数据库初始化成功: output/test_query.db
[INFO] 2025-07-31 14:23:59.789 - 检测结果已保存到数据库，会话ID: 1，损伤数量: 5
[INFO] 2025-07-31 14:23:59.789 - ✓ 数据查询测试通过
[INFO] 2025-07-31 14:23:59.790 - 测试事务处理...
[INFO] 2025-07-31 14:23:59.794 - 数据库表和索引创建成功
[INFO] 2025-07-31 14:23:59.794 - 数据库初始化成功: output/test_transaction.db
[INFO] 2025-07-31 14:23:59.795 - 检测结果已保存到数据库，会话ID: 1，损伤数量: 2
[INFO] 2025-07-31 14:23:59.795 - ✓ 事务处理测试通过
[INFO] 2025-07-31 14:23:59.796 - 测试数据完整性...
[INFO] 2025-07-31 14:23:59.801 - 数据库表和索引创建成功
[INFO] 2025-07-31 14:23:59.801 - 数据库初始化成功: output/test_integrity.db
[INFO] 2025-07-31 14:23:59.802 - 检测结果已保存到数据库，会话ID: 1，损伤数量: 7
[INFO] 2025-07-31 14:23:59.802 - ✓ 数据完整性测试通过
[INFO] 2025-07-31 14:23:59.803 - 测试并发访问...
[INFO] 2025-07-31 14:23:59.807 - 数据库表和索引创建成功
[INFO] 2025-07-31 14:23:59.807 - 数据库初始化成功: output/test_concurrent.db
[INFO] 2025-07-31 14:23:59.808 - 检测结果已保存到数据库，会话ID: 1，损伤数量: 2
[INFO] 2025-07-31 14:23:59.809 - 检测结果已保存到数据库，会话ID: 2，损伤数量: 2
[INFO] 2025-07-31 14:23:59.809 - 检测结果已保存到数据库，会话ID: 3，损伤数量: 2
[INFO] 2025-07-31 14:23:59.809 - ✓ 并发访问测试通过
[INFO] 2025-07-31 14:23:59.810 - 测试数据清理功能...
[INFO] 2025-07-31 14:23:59.815 - 数据库表和索引创建成功
[INFO] 2025-07-31 14:23:59.815 - 数据库初始化成功: output/test_cleanup.db
[INFO] 2025-07-31 14:23:59.816 - 检测结果已保存到数据库，会话ID: 1，损伤数量: 3
[INFO] 2025-07-31 14:23:59.817 - 清理了 1 条旧数据记录
[INFO] 2025-07-31 14:23:59.817 - ✓ 数据清理测试通过
[INFO] 2025-07-31 14:23:59.817 - ✓ 所有数据库功能测试通过
[INFO] 2025-07-31 14:23:59.817 - ✓ 数据库功能测试: 通过
[INFO] 2025-07-31 14:23:59.817 - --- 推流功能测试 ---
[INFO] 2025-07-31 14:23:59.817 - 开始推流功能测试...
[INFO] 2025-07-31 14:23:59.818 - === 开始离线推流功能测试 ===
[INFO] 2025-07-31 14:23:59.818 - 测试离线视频编码器...
[INFO] 2025-07-31 14:23:59.818 - 初始化FFmpeg库...
[libx264 @ 0x5f01fcb463c0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
[libx264 @ 0x5f01fcb463c0] profile Constrained Baseline, level 2.2, 4:2:0, 8-bit
[INFO] 2025-07-31 14:23:59.821 - VideoEncoder: 视频编码器初始化成功: h264 640x480@15fps, 1000kbps, preset=fast, profile=baseline
[INFO] 2025-07-31 14:23:59.821 - 视频编码器初始化成功
[INFO] 2025-07-31 14:23:59.821 - 编码器状态正确
[INFO] 2025-07-31 14:23:59.824 - 帧编码成功
[INFO] 2025-07-31 14:23:59.824 - 第一帧编码可能需要缓冲，尝试编码更多帧...
[INFO] 2025-07-31 14:23:59.830 - 编码数据验证成功，大小: 0 字节
[INFO] 2025-07-31 14:23:59.830 - 离线视频编码器测试通过
[INFO] 2025-07-31 14:23:59.832 - 测试推流管理器配置...
[INFO] 2025-07-31 14:23:59.832 - 配置验证: 编解码器=h264
[INFO] 2025-07-31 14:23:59.832 - 配置验证: 分辨率=640x480
[INFO] 2025-07-31 14:23:59.832 - 配置验证: 帧率=15
[INFO] 2025-07-31 14:23:59.833 - 配置验证: 码率=1000000
[INFO] 2025-07-31 14:23:59.833 - 推流管理器配置测试通过
[INFO] 2025-07-31 14:23:59.833 - 测试帧处理逻辑...
[INFO] 2025-07-31 14:23:59.835 - 有效帧 (索引 0): 640x480
[INFO] 2025-07-31 14:23:59.835 - 检测到空帧 (索引 1)
[INFO] 2025-07-31 14:23:59.835 - 有效帧 (索引 2): 320x240
[INFO] 2025-07-31 14:23:59.835 - 有效帧 (索引 3): 1920x1080
[INFO] 2025-07-31 14:23:59.835 - 帧处理统计: 有效帧=3, 无效帧=1
[INFO] 2025-07-31 14:23:59.836 - 帧处理逻辑测试通过
[INFO] 2025-07-31 14:23:59.836 - 测试质量控制逻辑...
[INFO] 2025-07-31 14:24:07.443 - 质量控制统计:
[INFO] 2025-07-31 14:24:07.443 -   总帧数: 100
[INFO] 2025-07-31 14:24:07.443 -   丢弃帧数: 0
[INFO] 2025-07-31 14:24:07.443 -   丢帧率: 0.000000%
[INFO] 2025-07-31 14:24:07.443 - 质量控制逻辑测试通过
[INFO] 2025-07-31 14:24:07.443 - 测试错误处理...
[libx264 @ 0x5f01fc96ef80] [IMGUTILS @ 0x7ffe2f2e9cd0] Picture size 0x0 is invalid
[libx264 @ 0x5f01fc96ef80] Ignoring invalid width/height values
[libx264 @ 0x5f01fc96ef80] [IMGUTILS @ 0x7ffe2f2e9ca0] Picture size 0x0 is invalid
[libx264 @ 0x5f01fc96ef80] dimensions not set
[ERROR] 2025-07-31 14:24:07.444 - VideoEncoder: 无法打开编码器 (Invalid argument)
[ERROR] 2025-07-31 14:24:07.444 - VideoEncoder: 编码器配置失败
[INFO] 2025-07-31 14:24:07.444 - 正确拒绝了无效配置
[INFO] 2025-07-31 14:24:07.444 - 错误处理测试通过
[INFO] 2025-07-31 14:24:07.444 - 测试性能指标...
[INFO] 2025-07-31 14:24:07.953 - 性能统计:
[INFO] 2025-07-31 14:24:07.953 -   处理帧数: 50
[INFO] 2025-07-31 14:24:07.953 -   总字节数: 2560000
[INFO] 2025-07-31 14:24:07.953 -   平均FPS: 98.425197
[INFO] 2025-07-31 14:24:07.953 -   平均码率: 40.314961 Mbps
[INFO] 2025-07-31 14:24:07.953 -   处理时间: 508 ms
[INFO] 2025-07-31 14:24:07.953 - 性能指标测试通过
[INFO] 2025-07-31 14:24:07.953 - === 所有离线推流功能测试通过 ===
[INFO] 2025-07-31 14:24:07.954 - ✓ 离线推流功能测试: 通过
[INFO] 2025-07-31 14:24:07.954 - === 开始推流功能测试 ===
[INFO] 2025-07-31 14:24:07.954 - 测试视频编码器...
[libx264 @ 0x5f01fcb46bc0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
[libx264 @ 0x5f01fcb46bc0] profile Constrained Baseline, level 2.2, 4:2:0, 8-bit
[INFO] 2025-07-31 14:24:07.959 - VideoEncoder: 视频编码器初始化成功: h264 640x480@15fps, 1000kbps, preset=fast, profile=baseline
[INFO] 2025-07-31 14:24:07.959 - 编码器初始化成功: h264 640x480@15fps, 1000kbps, preset=fast, profile=baseline
[INFO] 2025-07-31 14:24:07.962 - 编码测试成功，数据包大小: 0 bytes
[INFO] 2025-07-31 14:24:07.962 - VideoEncoder: 码率已调整为: 2000 kbps
[INFO] 2025-07-31 14:24:07.962 - 码率调整测试成功
[INFO] 2025-07-31 14:24:07.962 - VideoEncoder: 视频编码器已重置
[INFO] 2025-07-31 14:24:07.962 - 编码器重置测试成功
[INFO] 2025-07-31 14:24:07.962 - 测试推流客户端...
[INFO] 2025-07-31 14:24:07.962 - FFmpeg网络组件初始化完成
[INFO] 2025-07-31 14:24:08.086 - StreamClient: 推流客户端初始化成功: rtmp://localhost:1935/test
[INFO] 2025-07-31 14:24:08.086 - 推流客户端初始化成功
[INFO] 2025-07-31 14:24:08.086 - 推流地址: rtmp://localhost:1935/test
[INFO] 2025-07-31 14:24:08.086 - 观看地址: rtsp://localhost:8554/test
[INFO] 2025-07-31 14:24:08.210 - StreamClient: 推流客户端初始化成功: rtmp://localhost:1935/test
[INFO] 2025-07-31 14:24:08.210 - 配置更新测试成功
[INFO] 2025-07-31 14:24:08.210 - StreamClient: 推流客户端已断开连接
[INFO] 2025-07-31 14:24:08.210 - 测试推流管理器...
[libx264 @ 0x5f01fcaae0c0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
[libx264 @ 0x5f01fcaae0c0] profile Constrained Baseline, level 2.2, 4:2:0, 8-bit
[INFO] 2025-07-31 14:24:08.214 - VideoEncoder: 视频编码器初始化成功: h264 640x480@15fps, 1000kbps, preset=fast, profile=baseline
[INFO] 2025-07-31 14:24:08.356 - StreamClient: 推流客户端初始化成功: rtmp://localhost:1935/test
[INFO] 2025-07-31 14:24:08.356 - RTSPStreamManager: 推流管理器初始化成功: rtsp://localhost:8554/test
[INFO] 2025-07-31 14:24:08.356 - 推流管理器初始化成功
[INFO] 2025-07-31 14:24:08.356 - RTSP URL: rtsp://localhost:8554/test
[INFO] 2025-07-31 14:24:08.357 - StreamClient: 推流客户端已断开连接
[libx264 @ 0x5f01fcaae0c0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
[libx264 @ 0x5f01fcaae0c0] profile Constrained Baseline, level 2.2, 4:2:0, 8-bit
[INFO] 2025-07-31 14:24:08.360 - VideoEncoder: 视频编码器初始化成功: h264 640x480@15fps, 2000kbps, preset=fast, profile=baseline
[INFO] 2025-07-31 14:24:08.483 - StreamClient: 推流客户端初始化成功: rtmp://localhost:1935/test
[INFO] 2025-07-31 14:24:08.484 - RTSPStreamManager: 推流管理器初始化成功: rtsp://localhost:8554/test
[INFO] 2025-07-31 14:24:08.484 - 配置更新测试成功
[INFO] 2025-07-31 14:24:08.484 - 推流管理器基本功能测试成功
[INFO] 2025-07-31 14:24:08.484 - StreamClient: 推流客户端已断开连接
[INFO] 2025-07-31 14:24:08.485 - 测试质量控制功能...
[libx264 @ 0x5f01fcaae0c0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
[libx264 @ 0x5f01fcaae0c0] profile Constrained Baseline, level 2.2, 4:2:0, 8-bit
[INFO] 2025-07-31 14:24:08.489 - VideoEncoder: 视频编码器初始化成功: h264 640x480@15fps, 2000kbps, preset=fast, profile=baseline
[INFO] 2025-07-31 14:24:08.612 - StreamClient: 推流客户端初始化成功: rtmp://localhost:1935/live
[INFO] 2025-07-31 14:24:08.612 - RTSPStreamManager: 推流管理器初始化成功: rtsp://0.0.0.0:8554/live
[INFO] 2025-07-31 14:24:08.612 - 统计信息初始化正确
[INFO] 2025-07-31 14:24:08.612 - 缓冲区管理正常，未发生丢帧
[INFO] 2025-07-31 14:24:08.613 - 质量控制功能测试成功
[INFO] 2025-07-31 14:24:08.613 - StreamClient: 推流客户端已断开连接
[INFO] 2025-07-31 14:24:08.613 - === 所有推流功能测试通过 ===
[INFO] 2025-07-31 14:24:08.613 - ✓ 在线推流功能测试: 通过
[INFO] 2025-07-31 14:24:08.613 - === 开始错误处理和异常测试 ===
[INFO] 2025-07-31 14:24:08.613 - 测试配置错误处理...
[INFO] 2025-07-31 14:24:08.613 - 测试视频编码器配置错误...
[libx264 @ 0x5f01fcaae0c0] [IMGUTILS @ 0x7ffe2f2e9e50] Picture size 0x0 is invalid
[libx264 @ 0x5f01fcaae0c0] Ignoring invalid width/height values
[libx264 @ 0x5f01fcaae0c0] [IMGUTILS @ 0x7ffe2f2e9e20] Picture size 0x0 is invalid
[libx264 @ 0x5f01fcaae0c0] dimensions not set
[ERROR] 2025-07-31 14:24:08.613 - VideoEncoder: 无法打开编码器 (Invalid argument)
[ERROR] 2025-07-31 14:24:08.613 - VideoEncoder: 编码器配置失败
[INFO] 2025-07-31 14:24:08.613 - 正确拒绝了无效宽度配置
[ERROR] 2025-07-31 14:24:08.613 - VideoEncoder: 编码器已经初始化
[INFO] 2025-07-31 14:24:08.613 - 正确拒绝了无效高度配置
[ERROR] 2025-07-31 14:24:08.613 - VideoEncoder: 编码器已经初始化
[INFO] 2025-07-31 14:24:08.613 - 正确拒绝了无效帧率配置
[ERROR] 2025-07-31 14:24:08.613 - VideoEncoder: 编码器已经初始化
[INFO] 2025-07-31 14:24:08.613 - 正确拒绝了无效码率配置
[INFO] 2025-07-31 14:24:08.613 - 测试推流客户端配置错误...
[NULL @ 0x5f01fc979700] Unable to find a suitable output format for ''
[ERROR] 2025-07-31 14:24:08.613 - StreamClient: 无法分配输出格式上下文 (Invalid argument)
[ERROR] 2025-07-31 14:24:08.613 - StreamClient: FFmpeg初始化失败
[INFO] 2025-07-31 14:24:08.613 - 正确拒绝了空URL配置
[INFO] 2025-07-31 14:24:08.736 - StreamClient: 推流客户端初始化成功: rtmp://localhost:1935/live
[ERROR] 2025-07-31 14:24:08.737 - 应该拒绝无效编解码器配置
[INFO] 2025-07-31 14:24:08.737 - StreamClient: 推流客户端已断开连接
[ERROR] 2025-07-31 14:24:08.737 - 配置错误处理测试失败
[INFO] 2025-07-31 14:24:08.737 - 测试初始化错误处理...
[INFO] 2025-07-31 14:24:08.737 - 测试重复初始化...
[libx264 @ 0x5f01fcaae0c0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
[libx264 @ 0x5f01fcaae0c0] profile Constrained Baseline, level 2.2, 4:2:0, 8-bit
[INFO] 2025-07-31 14:24:08.742 - VideoEncoder: 视频编码器初始化成功: h264 640x480@15fps, 1000kbps, preset=fast, profile=baseline
[INFO] 2025-07-31 14:24:08.742 - 第一次初始化成功
[ERROR] 2025-07-31 14:24:08.742 - VideoEncoder: 编码器已经初始化
[INFO] 2025-07-31 14:24:08.742 - 正确拒绝了重复初始化
[INFO] 2025-07-31 14:24:08.742 - 测试在错误状态下的操作...
[ERROR] 2025-07-31 14:24:08.742 - VideoEncoder: 编码器未初始化
[INFO] 2025-07-31 14:24:08.742 - 正确拒绝了在未初始化状态下的编码
[INFO] 2025-07-31 14:24:08.742 - 初始化错误处理测试通过
[INFO] 2025-07-31 14:24:08.742 - 测试运行时错误处理...
[INFO] 2025-07-31 14:24:08.742 - 测试空帧处理...
[libx264 @ 0x5f01fcaae0c0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
[libx264 @ 0x5f01fcaae0c0] profile Constrained Baseline, level 2.2, 4:2:0, 8-bit
[INFO] 2025-07-31 14:24:08.745 - VideoEncoder: 视频编码器初始化成功: h264 640x480@15fps, 1000kbps, preset=fast, profile=baseline
[ERROR] 2025-07-31 14:24:08.745 - VideoEncoder: 输入帧为空
[INFO] 2025-07-31 14:24:08.745 - 正确拒绝了空帧
[INFO] 2025-07-31 14:24:08.745 - 测试错误尺寸帧处理...
[libx264 @ 0x5f01fcaae0c0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
[libx264 @ 0x5f01fcaae0c0] profile Constrained Baseline, level 2.2, 4:2:0, 8-bit
[INFO] 2025-07-31 14:24:08.748 - VideoEncoder: 视频编码器初始化成功: h264 640x480@15fps, 1000kbps, preset=fast, profile=baseline
[ERROR] 2025-07-31 14:24:08.748 - VideoEncoder: 输入帧尺寸不匹配: 320x240 vs 640x480
[ERROR] 2025-07-31 14:24:08.748 - VideoEncoder: 图像格式转换失败
[INFO] 2025-07-31 14:24:08.748 - 正确拒绝了错误尺寸的帧
[INFO] 2025-07-31 14:24:08.748 - 测试错误格式帧处理...
[libx264 @ 0x5f01fcaae0c0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
[libx264 @ 0x5f01fcaae0c0] profile Constrained Baseline, level 2.2, 4:2:0, 8-bit
[INFO] 2025-07-31 14:24:08.751 - VideoEncoder: 视频编码器初始化成功: h264 640x480@15fps, 1000kbps, preset=fast, profile=baseline
[WARN] 2025-07-31 14:24:08.752 - 接受了错误格式的帧（可能进行了自动转换）
[INFO] 2025-07-31 14:24:08.753 - 运行时错误处理测试通过
[INFO] 2025-07-31 14:24:08.753 - 测试资源清理...
[INFO] 2025-07-31 14:24:08.753 - 测试编码器资源清理...
[libx264 @ 0x5f01fcaae0c0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
[libx264 @ 0x5f01fcaae0c0] profile Constrained Baseline, level 2.2, 4:2:0, 8-bit
[INFO] 2025-07-31 14:24:08.755 - VideoEncoder: 视频编码器初始化成功: h264 640x480@15fps, 1000kbps, preset=fast, profile=baseline
[INFO] 2025-07-31 14:24:08.755 - 编码器初始化成功
[INFO] 2025-07-31 14:24:08.756 - 编码器已删除，资源应该已清理
[INFO] 2025-07-31 14:24:08.756 - 测试推流客户端资源清理...
[INFO] 2025-07-31 14:24:08.880 - StreamClient: 推流客户端初始化成功: rtmp://localhost:1935/live
[INFO] 2025-07-31 14:24:08.881 - 推流客户端初始化成功
[INFO] 2025-07-31 14:24:08.881 - StreamClient: 推流客户端已断开连接
[INFO] 2025-07-31 14:24:08.881 - 推流客户端已删除，资源应该已清理
[INFO] 2025-07-31 14:24:08.881 - 资源清理测试通过
[INFO] 2025-07-31 14:24:08.881 - 测试边界条件...
[INFO] 2025-07-31 14:24:08.881 - 测试极限配置...
[libx264 @ 0x5f01fcaae0c0] Bitrate 1 is extremely low, maybe you mean 1k
[libx264 @ 0x5f01fcaae0c0] width not divisible by 2 (1x1)
[ERROR] 2025-07-31 14:24:08.882 - VideoEncoder: 无法打开编码器 (Generic error in an external library)
[ERROR] 2025-07-31 14:24:08.882 - VideoEncoder: 编码器配置失败
[INFO] 2025-07-31 14:24:08.882 - 正确拒绝了极小配置
[ERROR] 2025-07-31 14:24:08.882 - VideoEncoder: 编码器已经初始化
[INFO] 2025-07-31 14:24:08.882 - 正确处理了极大配置
[INFO] 2025-07-31 14:24:08.882 - 边界条件测试通过
[INFO] 2025-07-31 14:24:08.882 - 测试异常恢复...
[INFO] 2025-07-31 14:24:08.882 - 测试编码器重新初始化...
[libx264 @ 0x5f01fcaae0c0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
[libx264 @ 0x5f01fcaae0c0] profile Constrained Baseline, level 2.2, 4:2:0, 8-bit
[INFO] 2025-07-31 14:24:08.887 - VideoEncoder: 视频编码器初始化成功: h264 640x480@15fps, 1000kbps, preset=fast, profile=baseline
[INFO] 2025-07-31 14:24:08.887 - 第一次初始化成功
[ERROR] 2025-07-31 14:24:08.887 - VideoEncoder: 编码器已经初始化
[WARN] 2025-07-31 14:24:08.887 - 重新初始化失败
[INFO] 2025-07-31 14:24:08.887 - 异常恢复测试通过
[ERROR] 2025-07-31 14:24:08.887 - === 部分错误处理和异常测试失败 ===
[ERROR] 2025-07-31 14:24:08.887 - ✗ 错误处理和异常测试: 失败
[INFO] 2025-07-31 14:24:08.887 - 推流功能测试完成
[INFO] 2025-07-31 14:24:08.887 - === 测试结果摘要 ===
[INFO] 2025-07-31 14:24:08.888 - 总测试数: 7
[INFO] 2025-07-31 14:24:08.888 - 通过测试: 6
[INFO] 2025-07-31 14:24:08.888 - 失败测试: 1
[INFO] 2025-07-31 14:24:08.888 - 成功率: 85.714286%
[ERROR] 2025-07-31 14:24:08.888 - 失败的测试:
[ERROR] 2025-07-31 14:24:08.888 -   - 错误处理和异常测试
[ERROR] 2025-07-31 14:24:08.888 - === 部分测试失败 ===
[INFO] 2025-07-31 14:24:08.888 - 系统正常退出
