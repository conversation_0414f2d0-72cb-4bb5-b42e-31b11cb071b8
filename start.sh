#!/bin/bash

# 拉吊索缺损识别系统一键启动脚本
# 功能：自动启动MediaMTX服务器，等待服务就绪后启动主程序
# 版本：v1.0.0
# 作者：故障检测系统开发团队

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置常量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"
MEDIAMTX_SCRIPT="$PROJECT_ROOT/scripts/setup_rtsp_server.sh"
MAIN_APP="$PROJECT_ROOT/build/bin/FaultDetectRefactored"
CONFIG_FILE="$PROJECT_ROOT/config/system_config.json"
PID_FILE="$PROJECT_ROOT/fault_detect_system.pid"

# 默认配置
DEFAULT_RTSP_HOST="*************"
DEFAULT_RTSP_PORT="8554"
MAX_WAIT_TIME=30
CHECK_INTERVAL=2

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
拉吊索缺损识别系统一键启动脚本

用法: $0 [选项]

选项:
    start           启动完整系统（默认）
    server-only     仅启动MediaMTX服务器
    app-only        仅启动主程序（假设服务器已运行）
    stop            停止所有服务
    restart         重启所有服务
    status          检查系统状态
    --help, -h      显示此帮助信息

示例:
    $0              # 启动完整系统
    $0 start        # 启动完整系统
    $0 server-only  # 仅启动RTSP服务器
    $0 stop         # 停止所有服务
    $0 status       # 检查系统状态

说明:
    此脚本会自动启动MediaMTX RTSP服务器，等待服务完全就绪后，
    再启动故障检测主程序，确保推流连接不会失败。

EOF
}

# 检查依赖
check_dependencies() {
    log_step "检查系统依赖..."
    
    local missing_deps=()
    
    # 检查必要的可执行文件
    if [[ ! -f "$MEDIAMTX_SCRIPT" ]]; then
        missing_deps+=("MediaMTX管理脚本: $MEDIAMTX_SCRIPT")
    fi
    
    if [[ ! -f "$MAIN_APP" ]]; then
        missing_deps+=("主程序: $MAIN_APP")
    fi
    
    if [[ ! -f "$CONFIG_FILE" ]]; then
        missing_deps+=("配置文件: $CONFIG_FILE")
    fi
    
    # 检查系统命令
    for cmd in netstat nc jq; do
        if ! command -v "$cmd" &> /dev/null; then
            missing_deps+=("系统命令: $cmd")
        fi
    done
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "缺少以下依赖："
        for dep in "${missing_deps[@]}"; do
            echo "  - $dep"
        done
        
        log_info "请先运行以下命令安装缺少的依赖："
        echo "  sudo apt update"
        echo "  sudo apt install -y netstat-persistent netcat-openbsd jq"
        echo "  ./build.sh  # 编译主程序"
        return 1
    fi
    
    log_success "依赖检查通过"
    return 0
}

# 读取配置文件
read_config() {
    log_step "读取系统配置..."
    
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_warning "配置文件不存在，使用默认配置"
        RTSP_HOST="$DEFAULT_RTSP_HOST"
        RTSP_PORT="$DEFAULT_RTSP_PORT"
        return 0
    fi
    
    # 使用jq解析JSON配置
    if command -v jq &> /dev/null; then
        RTSP_HOST=$(jq -r '.streaming.rtsp_server_address // "'"$DEFAULT_RTSP_HOST"'"' "$CONFIG_FILE" 2>/dev/null)
        RTSP_PORT=$(jq -r '.streaming.rtsp_server_port // "'"$DEFAULT_RTSP_PORT"'"' "$CONFIG_FILE" 2>/dev/null)
    else
        # 简单的grep解析作为备选方案
        RTSP_HOST=$(grep -o '"rtsp_server_address"[[:space:]]*:[[:space:]]*"[^"]*"' "$CONFIG_FILE" | cut -d'"' -f4 2>/dev/null || echo "$DEFAULT_RTSP_HOST")
        RTSP_PORT=$(grep -o '"rtsp_server_port"[[:space:]]*:[[:space:]]*[0-9]*' "$CONFIG_FILE" | grep -o '[0-9]*$' 2>/dev/null || echo "$DEFAULT_RTSP_PORT")
    fi
    
    # 验证配置
    if [[ -z "$RTSP_HOST" || "$RTSP_HOST" == "null" ]]; then
        RTSP_HOST="$DEFAULT_RTSP_HOST"
    fi
    
    if [[ -z "$RTSP_PORT" || "$RTSP_PORT" == "null" ]]; then
        RTSP_PORT="$DEFAULT_RTSP_PORT"
    fi
    
    log_info "RTSP服务器配置: $RTSP_HOST:$RTSP_PORT"
    return 0
}

# 检查端口是否监听
check_port() {
    local host="$1"
    local port="$2"
    local timeout="${3:-5}"
    
    # 使用netstat检查端口监听
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        return 0
    fi
    
    return 1
}

# 测试端口连通性
test_connectivity() {
    local host="$1"
    local port="$2"
    local timeout="${3:-5}"

    # 如果配置的主机不是localhost，先尝试localhost
    if [[ "$host" != "localhost" && "$host" != "127.0.0.1" ]]; then
        log_info "尝试连接配置的地址 $host:$port..."

        # 使用nc测试连接
        if command -v nc &> /dev/null; then
            if nc -z -w "$timeout" "$host" "$port" 2>/dev/null; then
                return 0
            fi
        fi

        log_warning "无法连接到配置的地址 $host:$port，尝试localhost..."

        # 尝试localhost
        if command -v nc &> /dev/null; then
            if nc -z -w "$timeout" "localhost" "$port" 2>/dev/null; then
                log_info "成功连接到 localhost:$port"
                return 0
            fi
        fi

        # 尝试127.0.0.1
        if command -v nc &> /dev/null; then
            if nc -z -w "$timeout" "127.0.0.1" "$port" 2>/dev/null; then
                log_info "成功连接到 127.0.0.1:$port"
                return 0
            fi
        fi
    else
        # 直接测试localhost
        if command -v nc &> /dev/null; then
            if nc -z -w "$timeout" "$host" "$port" 2>/dev/null; then
                return 0
            fi
        fi
    fi

    # 使用telnet作为备选方案
    if command -v timeout &> /dev/null && command -v telnet &> /dev/null; then
        if timeout "$timeout" telnet "$host" "$port" </dev/null &>/dev/null; then
            return 0
        fi
    fi

    return 1
}

# 等待服务启动
wait_for_service() {
    local host="$1"
    local port="$2"
    local service_name="$3"
    local max_wait="$4"
    
    log_step "等待 $service_name 服务启动 (最多等待 ${max_wait}s)..."
    
    local elapsed=0
    while [[ $elapsed -lt $max_wait ]]; do
        if check_port "$host" "$port"; then
            log_info "端口 $port 已开始监听"
            
            # 额外等待2秒确保服务完全就绪
            sleep 2
            
            # 测试连通性
            if test_connectivity "$host" "$port" 3; then
                log_success "$service_name 服务已就绪"
                return 0
            else
                log_warning "端口监听但连接测试失败，继续等待..."
            fi
        fi
        
        sleep $CHECK_INTERVAL
        elapsed=$((elapsed + CHECK_INTERVAL))
        echo -n "."
    done
    
    echo
    log_error "$service_name 服务启动超时"
    return 1
}

# 启动MediaMTX服务器
start_mediamtx() {
    log_step "启动MediaMTX RTSP服务器..."

    # 检查MediaMTX管理脚本是否可执行
    if [[ ! -x "$MEDIAMTX_SCRIPT" ]]; then
        chmod +x "$MEDIAMTX_SCRIPT"
    fi

    # 启动MediaMTX服务器
    if ! "$MEDIAMTX_SCRIPT" start; then
        log_error "MediaMTX服务器启动失败"
        return 1
    fi

    # 等待服务启动
    if ! wait_for_service "localhost" "$RTSP_PORT" "MediaMTX RTSP" "$MAX_WAIT_TIME"; then
        log_error "MediaMTX服务器启动验证失败"
        return 1
    fi

    log_success "MediaMTX服务器启动成功"
    return 0
}

# 启动主程序
start_main_app() {
    log_step "启动故障检测主程序..."

    # 检查主程序是否存在
    if [[ ! -f "$MAIN_APP" ]]; then
        log_error "主程序不存在: $MAIN_APP"
        log_info "请先运行 ./build.sh 编译项目"
        return 1
    fi

    # 检查主程序是否可执行
    if [[ ! -x "$MAIN_APP" ]]; then
        chmod +x "$MAIN_APP"
    fi

    # 切换到构建目录（主程序可能依赖相对路径）
    cd "$PROJECT_ROOT/build" || {
        log_error "无法切换到构建目录"
        return 1
    }

    log_info "启动主程序: $MAIN_APP"
    log_info "推流地址: rtsp://$RTSP_HOST:$RTSP_PORT/live"
    log_info "按 Ctrl+C 停止程序"
    echo

    # 启动主程序（前台运行）
    exec "$MAIN_APP"
}

# 启动主程序（后台运行）
start_main_app_background() {
    log_step "启动故障检测主程序（后台运行）..."

    # 检查主程序是否存在
    if [[ ! -f "$MAIN_APP" ]]; then
        log_error "主程序不存在: $MAIN_APP"
        log_info "请先运行 ./build.sh 编译项目"
        return 1
    fi

    # 检查主程序是否可执行
    if [[ ! -x "$MAIN_APP" ]]; then
        chmod +x "$MAIN_APP"
    fi

    # 切换到构建目录
    cd "$PROJECT_ROOT/build" || {
        log_error "无法切换到构建目录"
        return 1
    }

    # 启动主程序（后台运行）
    nohup "$MAIN_APP" > "$PROJECT_ROOT/fault_detect.log" 2>&1 &
    local app_pid=$!

    # 保存PID
    echo "$app_pid" > "$PID_FILE"

    # 等待程序启动
    sleep 3

    # 检查程序是否还在运行
    if kill -0 "$app_pid" 2>/dev/null; then
        log_success "主程序启动成功 (PID: $app_pid)"
        log_info "推流地址: rtsp://$RTSP_HOST:$RTSP_PORT/live"
        log_info "日志文件: $PROJECT_ROOT/fault_detect.log"
        return 0
    else
        log_error "主程序启动失败"
        rm -f "$PID_FILE"
        return 1
    fi
}

# 停止所有服务
stop_services() {
    log_step "停止所有服务..."

    local stopped_any=false

    # 停止主程序
    if [[ -f "$PID_FILE" ]]; then
        local app_pid=$(cat "$PID_FILE")
        if kill -0 "$app_pid" 2>/dev/null; then
            log_info "停止主程序 (PID: $app_pid)..."
            kill "$app_pid" 2>/dev/null || true
            sleep 2

            # 强制杀死如果还在运行
            if kill -0 "$app_pid" 2>/dev/null; then
                kill -9 "$app_pid" 2>/dev/null || true
            fi
            stopped_any=true
        fi
        rm -f "$PID_FILE"
    fi

    # 停止MediaMTX服务器
    if [[ -x "$MEDIAMTX_SCRIPT" ]]; then
        log_info "停止MediaMTX服务器..."
        "$MEDIAMTX_SCRIPT" stop || true
        stopped_any=true
    fi

    if $stopped_any; then
        log_success "所有服务已停止"
    else
        log_info "没有运行中的服务需要停止"
    fi

    return 0
}

# 检查系统状态
check_status() {
    log_step "检查系统状态..."

    echo
    echo "=== MediaMTX服务器状态 ==="
    if [[ -x "$MEDIAMTX_SCRIPT" ]]; then
        "$MEDIAMTX_SCRIPT" status || true
    else
        log_warning "MediaMTX管理脚本不可用"
    fi

    echo
    echo "=== 主程序状态 ==="
    if [[ -f "$PID_FILE" ]]; then
        local app_pid=$(cat "$PID_FILE")
        if kill -0 "$app_pid" 2>/dev/null; then
            log_success "主程序正在运行 (PID: $app_pid)"
        else
            log_warning "主程序进程不存在"
            rm -f "$PID_FILE"
        fi
    else
        log_info "主程序未运行"
    fi

    echo
    echo "=== 端口监听状态 ==="
    if check_port "localhost" "$RTSP_PORT"; then
        log_success "RTSP端口 $RTSP_PORT 正在监听"
    else
        log_warning "RTSP端口 $RTSP_PORT 未监听"
    fi

    echo
    echo "=== 配置信息 ==="
    log_info "RTSP服务器: $RTSP_HOST:$RTSP_PORT"
    log_info "推流地址: rtsp://$RTSP_HOST:$RTSP_PORT/live"
    log_info "配置文件: $CONFIG_FILE"
    log_info "主程序: $MAIN_APP"

    return 0
}

# 启动完整系统
start_full_system() {
    log_info "=== 拉吊索缺损识别系统启动 ==="
    echo

    # 检查依赖
    if ! check_dependencies; then
        return 1
    fi

    # 读取配置
    if ! read_config; then
        return 1
    fi

    # 启动MediaMTX服务器
    if ! start_mediamtx; then
        log_error "MediaMTX服务器启动失败，无法继续"
        return 1
    fi

    echo
    log_success "MediaMTX服务器已就绪，现在启动主程序..."
    echo

    # 启动主程序（前台运行）
    start_main_app
}

# 启动完整系统（后台模式）
start_full_system_background() {
    log_info "=== 拉吊索缺损识别系统启动（后台模式）==="
    echo

    # 检查依赖
    if ! check_dependencies; then
        return 1
    fi

    # 读取配置
    if ! read_config; then
        return 1
    fi

    # 启动MediaMTX服务器
    if ! start_mediamtx; then
        log_error "MediaMTX服务器启动失败，无法继续"
        return 1
    fi

    echo
    log_success "MediaMTX服务器已就绪，现在启动主程序..."
    echo

    # 启动主程序（后台运行）
    if start_main_app_background; then
        echo
        log_success "系统启动完成！"
        log_info "使用 '$0 status' 检查系统状态"
        log_info "使用 '$0 stop' 停止系统"
        return 0
    else
        return 1
    fi
}

# 仅启动服务器
start_server_only() {
    log_info "=== 启动MediaMTX RTSP服务器 ==="
    echo

    # 检查依赖
    if ! check_dependencies; then
        return 1
    fi

    # 读取配置
    if ! read_config; then
        return 1
    fi

    # 启动MediaMTX服务器
    if start_mediamtx; then
        echo
        log_success "MediaMTX服务器启动完成！"
        log_info "推流地址: rtsp://$RTSP_HOST:$RTSP_PORT/live"
        log_info "使用 '$0 app-only' 启动主程序"
        log_info "使用 '$0 stop' 停止服务器"
        return 0
    else
        return 1
    fi
}

# 仅启动主程序
start_app_only() {
    log_info "=== 启动故障检测主程序 ==="
    echo

    # 检查依赖
    if ! check_dependencies; then
        return 1
    fi

    # 读取配置
    if ! read_config; then
        return 1
    fi

    # 检查MediaMTX服务器是否运行
    if ! check_port "localhost" "$RTSP_PORT"; then
        log_error "MediaMTX服务器未运行（端口 $RTSP_PORT 未监听）"
        log_info "请先运行 '$0 server-only' 启动服务器"
        return 1
    fi

    # 测试连通性
    if ! test_connectivity "$RTSP_HOST" "$RTSP_PORT" 5; then
        log_error "无法连接到MediaMTX服务器 ($RTSP_HOST:$RTSP_PORT)"
        log_info "请检查服务器状态和网络配置"
        return 1
    fi

    log_success "MediaMTX服务器连接正常，启动主程序..."
    echo

    # 启动主程序
    start_main_app
}

# 重启系统
restart_system() {
    log_info "=== 重启拉吊索缺损识别系统 ==="
    echo

    # 停止所有服务
    stop_services

    echo
    log_info "等待3秒后重新启动..."
    sleep 3
    echo

    # 启动完整系统
    start_full_system_background
}

# 主函数
main() {
    # 设置信号处理
    trap 'log_info "收到中断信号，正在清理..."; stop_services; exit 130' INT TERM

    # 解析命令行参数
    case "${1:-start}" in
        start)
            start_full_system
            ;;
        start-bg|background)
            start_full_system_background
            ;;
        server-only|server)
            start_server_only
            ;;
        app-only|app)
            start_app_only
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_system
            ;;
        status)
            read_config
            check_status
            ;;
        --help|-h|help)
            show_help
            ;;
        *)
            log_error "未知参数: $1"
            echo
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
